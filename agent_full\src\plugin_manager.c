#include "plugin_manager.h"
#include "logger.h"
#include "utils.h"

#ifdef _WIN32
#include <io.h>
#include <direct.h>
#else
#include <dirent.h>
#include <sys/stat.h>
#endif

AgentResult plugin_manager_init(AgentContext* ctx)
{
    if (!ctx) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    // Initialize plugin array
    memset(ctx->plugins, 0, sizeof(ctx->plugins));
    ctx->plugin_count = 0;
    
    LOG_INFO_MSG("Plugin manager initialized");
    return AGENT_SUCCESS;
}

void plugin_manager_cleanup(AgentContext* ctx)
{
    if (!ctx) {
        return;
    }
    
    // Unload all plugins
    for (int i = 0; i < ctx->plugin_count; i++) {
        if (ctx->plugins[i].state != PLUGIN_STATE_UNLOADED) {
            plugin_manager_unload_plugin(ctx, ctx->plugins[i].name);
        }
    }
    
    ctx->plugin_count = 0;
    LOG_INFO_MSG("Plugin manager cleanup completed");
}

AgentResult plugin_manager_scan_directory(AgentContext* ctx)
{
    if (!ctx) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    LOG_DEBUG_MSG("Scanning plugin directory: %s", ctx->config.plugin_directory);
    
#ifdef _WIN32
    char search_pattern[MAX_PATH_LEN];
    snprintf(search_pattern, sizeof(search_pattern), "%s\\*.dll", ctx->config.plugin_directory);
    
    WIN32_FIND_DATAA find_data;
    HANDLE find_handle = FindFirstFileA(search_pattern, &find_data);
    
    if (find_handle == INVALID_HANDLE_VALUE) {
        LOG_WARN_MSG("No plugins found in directory: %s", ctx->config.plugin_directory);
        return AGENT_SUCCESS;
    }
    
    do {
        if (!(find_data.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY)) {
            char plugin_path[MAX_PATH_LEN];
            snprintf(plugin_path, sizeof(plugin_path), "%s\\%s", 
                    ctx->config.plugin_directory, find_data.cFileName);
            
            // Check if plugin is already loaded
            char plugin_name[64];
            strncpy(plugin_name, find_data.cFileName, sizeof(plugin_name) - 1);
            plugin_name[sizeof(plugin_name) - 1] = '\0';
            
            // Remove .dll extension
            char* dot = strrchr(plugin_name, '.');
            if (dot) *dot = '\0';
            
            if (!plugin_manager_find_plugin(ctx, plugin_name)) {
                LOG_INFO_MSG("Found new plugin: %s", plugin_path);
                plugin_manager_load_plugin(ctx, plugin_path);
            }
        }
    } while (FindNextFileA(find_handle, &find_data));
    
    FindClose(find_handle);
#else
    DIR* dir = opendir(ctx->config.plugin_directory);
    if (!dir) {
        LOG_WARN_MSG("Cannot open plugin directory: %s", ctx->config.plugin_directory);
        return AGENT_ERROR_SYSTEM;
    }
    
    struct dirent* entry;
    while ((entry = readdir(dir)) != NULL) {
        if (strstr(entry->d_name, ".so")) {
            char plugin_path[MAX_PATH_LEN];
            snprintf(plugin_path, sizeof(plugin_path), "%s/%s", 
                    ctx->config.plugin_directory, entry->d_name);
            
            char plugin_name[64];
            strncpy(plugin_name, entry->d_name, sizeof(plugin_name) - 1);
            plugin_name[sizeof(plugin_name) - 1] = '\0';
            
            char* dot = strrchr(plugin_name, '.');
            if (dot) *dot = '\0';
            
            if (!plugin_manager_find_plugin(ctx, plugin_name)) {
                LOG_INFO_MSG("Found new plugin: %s", plugin_path);
                plugin_manager_load_plugin(ctx, plugin_path);
            }
        }
    }
    
    closedir(dir);
#endif
    
    // Plugin scan completed
    return AGENT_SUCCESS;
}

AgentResult plugin_manager_load_plugin(AgentContext* ctx, const char* plugin_path)
{
    if (!ctx || !plugin_path || ctx->plugin_count >= MAX_PLUGINS) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    // Extract plugin name from path
    const char* filename = strrchr(plugin_path, PATH_SEPARATOR[0]);
    if (!filename) {
        filename = plugin_path;
    } else {
        filename++; // Skip separator
    }
    
    PluginInfo* plugin = &ctx->plugins[ctx->plugin_count];
    memset(plugin, 0, sizeof(PluginInfo));
    
    strncpy(plugin->name, filename, sizeof(plugin->name) - 1);
    strncpy(plugin->path, plugin_path, sizeof(plugin->path) - 1);
    
    // Remove extension from name
    char* dot = strrchr(plugin->name, '.');
    if (dot) *dot = '\0';
    
#ifdef _WIN32
    plugin->handle = LoadLibraryA(plugin_path);
    if (!plugin->handle) {
        snprintf(plugin->last_error, sizeof(plugin->last_error), 
                "Failed to load DLL: %lu", GetLastError());
        plugin->state = PLUGIN_STATE_ERROR;
        LOG_ERROR_MSG("Failed to load plugin %s: %s", plugin->name, plugin->last_error);
        return AGENT_ERROR_PLUGIN;
    }

    // Load function pointers for unified push interface
    plugin->interface.init_func = (int(*)(void*))GetProcAddress(plugin->handle, "plugin_init");
    plugin->interface.cleanup_func = (int(*)(void))GetProcAddress(plugin->handle, "plugin_cleanup");
    plugin->interface.register_push_callback_func = (int(*)(int(*)(const char*, const char*, const char*, int)))
        GetProcAddress(plugin->handle, "plugin_register_push_callback");
    plugin->interface.start_push_mode_func = (int(*)(void))GetProcAddress(plugin->handle, "plugin_start_push_mode");
    plugin->interface.stop_push_mode_func = (int(*)(void))GetProcAddress(plugin->handle, "plugin_stop_push_mode");
    plugin->interface.configure_push_func = (int(*)(const char*))GetProcAddress(plugin->handle, "plugin_configure_push");
    plugin->interface.get_plugin_info_func = (const char*(*)(void))GetProcAddress(plugin->handle, "plugin_get_plugin_info");
#else
    plugin->handle = dlopen(plugin_path, RTLD_LAZY);
    if (!plugin->handle) {
        snprintf(plugin->last_error, sizeof(plugin->last_error),
                "Failed to load SO: %s", dlerror());
        plugin->state = PLUGIN_STATE_ERROR;
        LOG_ERROR_MSG("Failed to load plugin %s: %s", plugin->name, plugin->last_error);
        return AGENT_ERROR_PLUGIN;
    }

    plugin->interface.init_func = (int(*)(void*))dlsym(plugin->handle, "plugin_init");
    plugin->interface.cleanup_func = (int(*)(void))dlsym(plugin->handle, "plugin_cleanup");
    plugin->interface.register_push_callback_func = (int(*)(int(*)(const char*, const char*, const char*, int)))
        dlsym(plugin->handle, "plugin_register_push_callback");
    plugin->interface.start_push_mode_func = (int(*)(void))dlsym(plugin->handle, "plugin_start_push_mode");
    plugin->interface.stop_push_mode_func = (int(*)(void))dlsym(plugin->handle, "plugin_stop_push_mode");
    plugin->interface.configure_push_func = (int(*)(const char*))dlsym(plugin->handle, "plugin_configure_push");
    plugin->interface.get_plugin_info_func = (const char*(*)(void))dlsym(plugin->handle, "plugin_get_plugin_info");
#endif
    
    // Initialize plugin with agent context
    if (plugin->interface.init_func) {
        int result = plugin->interface.init_func(ctx);
        if (result == 0) {
            plugin->state = PLUGIN_STATE_LOADED;
            plugin->push_mode_enabled = false;
            ctx->plugin_count++;
            ctx->stats.plugin_count = ctx->plugin_count;

            LOG_INFO_MSG("Plugin loaded successfully: %s", plugin->name);
            return AGENT_SUCCESS;
        } else {
            snprintf(plugin->last_error, sizeof(plugin->last_error),
                    "Plugin initialization failed: %d", result);
            plugin->state = PLUGIN_STATE_ERROR;
        }
    } else {
        strcpy(plugin->last_error, "Plugin init function not found");
        plugin->state = PLUGIN_STATE_ERROR;
    }
    
    // Cleanup on error
#ifdef _WIN32
    FreeLibrary(plugin->handle);
#else
    dlclose(plugin->handle);
#endif
    
    LOG_ERROR_MSG("Failed to initialize plugin %s: %s", plugin->name, plugin->last_error);
    return AGENT_ERROR_PLUGIN;
}

AgentResult plugin_manager_unload_plugin(AgentContext* ctx, const char* plugin_name)
{
    if (!ctx || !plugin_name) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    PluginInfo* plugin = plugin_manager_find_plugin(ctx, plugin_name);
    if (!plugin) {
        return AGENT_ERROR_NOT_FOUND;
    }
    
    // Stop push mode if enabled
    if (plugin->push_mode_enabled && plugin->interface.stop_push_mode_func) {
        plugin->interface.stop_push_mode_func();
        plugin->push_mode_enabled = false;
    }

    // Cleanup plugin
    if (plugin->interface.cleanup_func && plugin->state == PLUGIN_STATE_LOADED) {
        plugin->interface.cleanup_func();
    }
    
    // Unload library
    if (plugin->handle) {
#ifdef _WIN32
        FreeLibrary(plugin->handle);
#else
        dlclose(plugin->handle);
#endif
    }
    
    plugin->state = PLUGIN_STATE_UNLOADED;
    LOG_INFO_MSG("Plugin unloaded: %s", plugin->name);
    
    return AGENT_SUCCESS;
}

// 旧的执行函数已移除，现在使用统一推送模式
// 插件通过回调主动推送数据，不再需要定期执行

PluginInfo* plugin_manager_find_plugin(AgentContext* ctx, const char* plugin_name)
{
    if (!ctx || !plugin_name) {
        return NULL;
    }
    
    for (int i = 0; i < ctx->plugin_count; i++) {
        if (strcmp(ctx->plugins[i].name, plugin_name) == 0) {
            return &ctx->plugins[i];
        }
    }
    
    return NULL;
}

// 旧的插件执行和数据收集函数已移除
// 现在使用统一推送模式，插件主动推送数据

void plugin_manager_get_status(AgentContext* ctx, char* buffer, size_t buffer_size)
{
    if (!ctx || !buffer) {
        return;
    }
    
    snprintf(buffer, buffer_size,
            "Plugin Manager Status (Unified Push Mode):\n"
            "- Total plugins: %d\n"
            "- Plugin directory: %s\n",
            ctx->plugin_count,
            ctx->config.plugin_directory);
    
    for (int i = 0; i < ctx->plugin_count; i++) {
        PluginInfo* plugin = &ctx->plugins[i];
        char status_line[256];
        
        const char* state_str;
        switch (plugin->state) {
            case PLUGIN_STATE_UNLOADED: state_str = "Unloaded"; break;
            case PLUGIN_STATE_LOADED: state_str = "Loaded"; break;
            case PLUGIN_STATE_RUNNING: state_str = "Running"; break;
            case PLUGIN_STATE_ERROR: state_str = "Error"; break;
            default: state_str = "Unknown"; break;
        }
        
        snprintf(status_line, sizeof(status_line),
                "- %s: %s (%s)\n", plugin->name, state_str, plugin->path);
        
        strncat(buffer, status_line, buffer_size - strlen(buffer) - 1);
    }
}

// 启动所有插件的推送模式
AgentResult start_all_plugin_push_mode(AgentContext* ctx)
{
    if (!ctx) {
        return AGENT_ERROR_INVALID_PARAM;
    }

    LOG_INFO_MSG("Starting push mode for all plugins...");

    int success_count = 0;
    int error_count = 0;

    for (int i = 0; i < ctx->plugin_count; i++) {
        PluginInfo* plugin = &ctx->plugins[i];

        if (plugin->state != PLUGIN_STATE_LOADED && plugin->state != PLUGIN_STATE_RUNNING) {
            continue;
        }

        // 注册推送回调
        if (plugin->interface.register_push_callback_func) {
            int result = plugin->interface.register_push_callback_func(agent_push_data);
            if (result != 0) {
                LOG_WARN_MSG("Failed to register push callback for plugin: %s", plugin->name);
                error_count++;
                continue;
            }
        }

        // 启动推送模式
        if (plugin->interface.start_push_mode_func) {
            int result = plugin->interface.start_push_mode_func();
            if (result == 0) {
                plugin->push_mode_enabled = true;
                success_count++;
                LOG_INFO_MSG("Push mode started for plugin: %s", plugin->name);
            } else {
                LOG_ERROR_MSG("Failed to start push mode for plugin: %s", plugin->name);
                error_count++;
            }
        } else {
            LOG_WARN_MSG("Plugin %s does not support push mode", plugin->name);
            error_count++;
        }
    }

    LOG_INFO_MSG("Push mode startup completed: %d success, %d errors", success_count, error_count);

    return (success_count > 0) ? AGENT_SUCCESS : AGENT_ERROR_PLUGIN;
}

// 停止所有插件的推送模式
void stop_all_plugin_push_mode(AgentContext* ctx)
{
    if (!ctx) {
        return;
    }

    LOG_INFO_MSG("Stopping push mode for all plugins...");

    for (int i = 0; i < ctx->plugin_count; i++) {
        PluginInfo* plugin = &ctx->plugins[i];

        if (!plugin->push_mode_enabled) {
            continue;
        }

        // 停止推送模式
        if (plugin->interface.stop_push_mode_func) {
            plugin->interface.stop_push_mode_func();
            plugin->push_mode_enabled = false;
            LOG_INFO_MSG("Push mode stopped for plugin: %s", plugin->name);
        }
    }

    LOG_INFO_MSG("All plugin push modes stopped");
}

// 处理控制消息（非数据消息）
AgentResult message_handler_process_control(AgentContext* ctx, const char* message)
{
    if (!ctx || !message) {
        return AGENT_ERROR_INVALID_PARAM;
    }

    LOG_DEBUG_MSG("Processing control message: %.100s", message);

    // 解析消息类型
    // 这里可以处理配置更新、插件控制等消息
    // 由于是推送模式，主要处理控制类消息，不再处理数据请求

    // 简单的消息类型检查
    if (strstr(message, "\"type\":\"config_update\"")) {
        LOG_INFO_MSG("Received config update message");
        // 处理配置更新
        return AGENT_SUCCESS;
    } else if (strstr(message, "\"type\":\"plugin_control\"")) {
        LOG_INFO_MSG("Received plugin control message");
        // 处理插件控制
        return AGENT_SUCCESS;
    } else if (strstr(message, "\"type\":\"system_command\"")) {
        LOG_INFO_MSG("Received system command message");
        // 处理系统命令
        return AGENT_SUCCESS;
    } else {
        LOG_DEBUG_MSG("Unknown control message type, ignoring");
        return AGENT_SUCCESS;
    }
}