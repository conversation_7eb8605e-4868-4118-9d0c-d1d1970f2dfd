#ifndef PLUGIN_MANAGER_H
#define PLUGIN_MANAGER_H

#include "agent.h"

// Plugin manager functions
AgentResult plugin_manager_init(AgentContext* ctx);
void plugin_manager_cleanup(AgentContext* ctx);
AgentResult plugin_manager_scan_directory(AgentContext* ctx);
AgentResult plugin_manager_load_plugin(AgentContext* ctx, const char* plugin_path);
AgentResult plugin_manager_unload_plugin(AgentContext* ctx, const char* plugin_name);
PluginInfo* plugin_manager_find_plugin(AgentContext* ctx, const char* plugin_name);
void plugin_manager_get_status(AgentContext* ctx, char* buffer, size_t buffer_size);

// 统一推送模式管理
AgentResult start_all_plugin_push_mode(AgentContext* ctx);
void stop_all_plugin_push_mode(AgentContext* ctx);
AgentResult message_handler_process_control(AgentContext* ctx, const char* message);

#endif // PLUGIN_MANAGER_H