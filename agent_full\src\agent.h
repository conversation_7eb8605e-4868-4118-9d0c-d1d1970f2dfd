#ifndef AGENT_H
#define AGENT_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <time.h>

#ifdef _WIN32
    #define WIN32_LEAN_AND_MEAN
    #include <windows.h>
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
    #define SLEEP_MS(ms) Sleep(ms)
    #define GET_LAST_ERROR() WSAGetLastError()
    #define PATH_SEPARATOR "\\"
#else
    #include <unistd.h>
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <netdb.h>
    #include <dlfcn.h>
    #include <pthread.h>
    #include <errno.h>
    #include <sys/time.h>
    #define SLEEP_MS(ms) usleep((ms) * 1000)
    #define GET_LAST_ERROR() errno
    #define PATH_SEPARATOR "/"
    #define SOCKET int
    #define INVALID_SOCKET -1
    #define SOCKET_ERROR -1
    #define closesocket close
#endif

// Constants
#define MAX_BUFFER_SIZE 8192
#define MAX_MESSAGE_SIZE 4096
#define MAX_HOSTNAME_LEN 256
#define MAX_PATH_LEN 512
#define MAX_AGENT_ID_LEN 64
#define MAX_PLUGINS 16
#define RECONNECT_INTERVAL_MS 5000
#define MAX_RECONNECT_ATTEMPTS 10

// 统一推送相关常量
#define MAX_PUSH_THREADS 4
#define MAX_PUSH_QUEUE_SIZE 1000
#define MAX_SOURCE_NAME_LEN 64
#define MAX_DATA_TYPE_LEN 32
#define MAX_MESSAGE_ID_LEN 64
#define DEFAULT_PUSH_TIMEOUT_MS 5000
#define MAX_RETRY_COUNT 3

// Error codes
typedef enum {
    AGENT_SUCCESS = 0,
    AGENT_ERROR_INVALID_PARAM = -1,
    AGENT_ERROR_MEMORY = -2,
    AGENT_ERROR_NETWORK = -3,
    AGENT_ERROR_WEBSOCKET = -4,
    AGENT_ERROR_CONFIG = -5,
    AGENT_ERROR_SYSTEM = -6,
    AGENT_ERROR_PLUGIN = -7,
    AGENT_ERROR_NOT_FOUND = -8,
    AGENT_ERROR_TIMEOUT = -9,
    AGENT_ERROR_QUEUE_FULL = -10,
    AGENT_ERROR_PUSH_FAILED = -11,
    AGENT_ERROR_INVALID_STATE = -12
} AgentResult;

// Log levels
typedef enum {
    LOG_DEBUG = 0,
    LOG_INFO = 1,
    LOG_WARN = 2,
    LOG_ERROR = 3
} LogLevel;

// Agent state
typedef enum {
    AGENT_STATE_DISCONNECTED = 0,
    AGENT_STATE_CONNECTING = 1,
    AGENT_STATE_CONNECTED = 2,
    AGENT_STATE_ERROR = 3,
    AGENT_STATE_SHUTDOWN = 4
} AgentState;

// Plugin state
typedef enum {
    PLUGIN_STATE_UNLOADED = 0,
    PLUGIN_STATE_LOADED = 1,
    PLUGIN_STATE_RUNNING = 2,
    PLUGIN_STATE_ERROR = 3
} PluginState;

// 统一推送数据结构
typedef struct {
    char source_type[32];           // "system", "plugin", "heartbeat", "error"
    char source_name[MAX_SOURCE_NAME_LEN];  // 具体来源名称
    char data_type[MAX_DATA_TYPE_LEN];      // "heartbeat", "system_info", "camera_frame", "file_event"
    char* data;                     // JSON格式的数据
    int priority;                   // 优先级 1-10
    uint64_t timestamp;             // 时间戳
    char message_id[MAX_MESSAGE_ID_LEN];    // 消息唯一ID
    int retry_count;                // 重试次数
} UnifiedPushData;

// 消息队列结构
typedef struct {
    UnifiedPushData* messages;
    int capacity;
    int count;
    int head;
    int tail;
#ifdef _WIN32
    CRITICAL_SECTION mutex;
    HANDLE semaphore;
#else
    pthread_mutex_t mutex;
    pthread_cond_t condition;
#endif
} MessageQueue;

// 统一推送管理器
typedef struct {
    MessageQueue push_queue;
#ifdef _WIN32
    HANDLE push_threads[MAX_PUSH_THREADS];
#else
    pthread_t push_threads[MAX_PUSH_THREADS];
#endif
    int thread_count;
    bool enabled;
    volatile bool running;

    // 推送统计
    uint64_t total_pushed;
    uint64_t failed_pushes;
    uint64_t retry_count;
    uint64_t queue_overflows;
} UnifiedPushManager;

// 统一插件接口（新的推送模式）
typedef struct {
    // 基础生命周期
    int (*init_func)(void* agent_context);
    int (*cleanup_func)(void);

    // 统一推送接口
    int (*register_push_callback_func)(int (*push_callback)(const char* source_name,
                                                            const char* data_type,
                                                            const char* data,
                                                            int priority));
    int (*start_push_mode_func)(void);
    int (*stop_push_mode_func)(void);
    int (*configure_push_func)(const char* config_json);

    // 插件信息
    const char* (*get_plugin_info_func)(void);
} PluginInterface;

// Plugin info
typedef struct {
    char name[64];
    char path[MAX_PATH_LEN];
    PluginState state;
    void* handle;
    char last_error[256];
    bool push_mode_enabled;

    // 统一插件接口
    PluginInterface interface;
} PluginInfo;

// Agent configuration
typedef struct {
    char server_host[MAX_HOSTNAME_LEN];
    int server_port;
    char agent_id[MAX_AGENT_ID_LEN];
    int reconnect_interval;
    int max_reconnect_attempts;
    LogLevel log_level;
    char plugin_directory[MAX_PATH_LEN];

    // 统一推送配置
    bool push_enabled;
    int push_queue_size;
    int push_thread_count;
    int max_retry_count;
    int push_timeout_ms;
    bool enable_data_compression;
} AgentConfig;

// Agent statistics
typedef struct {
    uint64_t start_time;
    uint64_t uptime;
    uint64_t messages_sent;
    uint64_t messages_received;
    uint64_t bytes_sent;
    uint64_t bytes_received;
    int reconnect_count;
    int plugin_count;
    int error_count;
    uint32_t memory_usage;
    float cpu_usage;

    // 推送统计
    uint64_t total_pushed;
    uint64_t failed_pushes;
    uint64_t retry_count;
    uint64_t queue_overflows;
} AgentStats;

// Agent context
typedef struct {
    AgentConfig config;
    AgentState state;
    AgentStats stats;
    SOCKET websocket;
    int reconnect_count;
    uint64_t start_time;
    bool running;
    char last_error[256];

    // 兼容性字段（保留用于过渡期）
    uint64_t last_heartbeat;
    uint64_t last_plugin_scan;

    // 统一推送管理器
    UnifiedPushManager push_manager;

    // Plugin management (简化)
    PluginInfo plugins[MAX_PLUGINS];
    int plugin_count;
} AgentContext;

// Global functions
uint64_t get_timestamp_ms(void);
void format_timestamp(char* buffer, size_t size);
void agent_set_error(AgentContext* ctx, AgentResult error_code, const char* message);

// 统一推送API函数声明
AgentResult unified_push_manager_init(UnifiedPushManager* manager, int queue_size, int thread_count);
void unified_push_manager_cleanup(UnifiedPushManager* manager);
AgentResult unified_push_manager_start(UnifiedPushManager* manager, AgentContext* ctx);
void unified_push_manager_stop(UnifiedPushManager* manager);
AgentResult unified_push_manager_pause(UnifiedPushManager* manager);
AgentResult unified_push_manager_resume(UnifiedPushManager* manager);

// 消息队列函数
AgentResult message_queue_init(MessageQueue* queue, int capacity);
void message_queue_cleanup(MessageQueue* queue);
AgentResult message_queue_push(MessageQueue* queue, const UnifiedPushData* data);
bool message_queue_pop(MessageQueue* queue, UnifiedPushData* data, int timeout_ms);
int message_queue_size(const MessageQueue* queue);
bool message_queue_is_full(const MessageQueue* queue);

// Agent推送API（供插件调用）
int agent_push_data(const char* source_name, const char* data_type, const char* data, int priority);
AgentResult agent_register_global_context(AgentContext* ctx);

// 系统级推送插件函数
AgentResult start_system_push_plugins(AgentContext* ctx);
void stop_system_push_plugins(void);
AgentResult start_heartbeat_plugin(AgentContext* ctx, int interval_ms);
void stop_heartbeat_plugin(void);
AgentResult start_system_info_plugin(AgentContext* ctx, int check_interval_ms);
void stop_system_info_plugin(void);

#endif // AGENT_H