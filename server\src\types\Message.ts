export enum MessageType {
  HEARTBEAT = 'heartbeat',
  PLUGIN_DOWNLOAD = 'plugin_download',
  PLUGIN_EXECUTE = 'plugin_execute',
  PLUGIN_STOP = 'plugin_stop',
  DATA_UPLOAD = 'data_upload',
  STATUS_REPORT = 'status_report',
  SYSTEM_INFO = 'system_info',
  ERROR = 'error',
  ACK = 'ack',
  UNIFIED_PUSH = 'unified_push'
}

export interface BaseMessage {
  type: MessageType;
  timestamp: number;
  agentId: string;
  messageId: string;
  checksum?: string;
}

export interface HeartbeatMessage extends BaseMessage {
  type: MessageType.HEARTBEAT;
  data: {
    status: 'alive';
    systemLoad: number;
    memoryUsage: number;
    cpuUsage: number;
    activePlugins: number;
  };
}

export interface PluginDownloadMessage extends BaseMessage {
  type: MessageType.PLUGIN_DOWNLOAD;
  data: {
    pluginId: string;
    pluginName: string;
    pluginData: string; // base64编码的DLL数据
    checksum: string;
    autoExecute: boolean;
    priority: number;
  };
}

export interface PluginExecuteMessage extends BaseMessage {
  type: MessageType.PLUGIN_EXECUTE;
  data: {
    pluginId: string;
    parameters?: Record<string, any>;
    timeout?: number;
  };
}

export interface DataUploadMessage extends BaseMessage {
  type: MessageType.DATA_UPLOAD;
  data: {
    pluginId: string;
    dataType: string;
    payload: string; // base64编码的数据
    metadata?: Record<string, any>;
    compressed?: boolean;
  };
}

export interface StatusReportMessage extends BaseMessage {
  type: MessageType.STATUS_REPORT;
  data: {
    plugins: Array<{
      id: string;
      status: 'running' | 'stopped' | 'error';
      lastExecution?: number;
      errorMessage?: string;
    }>;
    systemMetrics: {
      cpuUsage: number;
      memoryUsage: number;
      diskUsage: number;
      networkIO: {
        bytesReceived: number;
        bytesSent: number;
      };
    };
  };
}

export interface ErrorMessage extends BaseMessage {
  type: MessageType.ERROR;
  data: {
    errorCode: string;
    errorMessage: string;
    details?: Record<string, any>;
  };
}

export interface AckMessage extends BaseMessage {
  type: MessageType.ACK;
  data: {
    originalMessageId: string;
    success: boolean;
    error?: string;
  };
}

export interface UnifiedPushMessage extends BaseMessage {
  type: MessageType.UNIFIED_PUSH;
  sourceType: string;
  sourceName: string;
  dataType: string;
  payload: string;
  priority: number;
}

export type Message =
  | HeartbeatMessage
  | PluginDownloadMessage
  | PluginExecuteMessage
  | DataUploadMessage
  | StatusReportMessage
  | ErrorMessage
  | AckMessage
  | UnifiedPushMessage;