#include "config.h"
#include "logger.h"

AgentResult config_init(AgentConfig* config)
{
    if (!config) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    config_set_defaults(config);
    return AGENT_SUCCESS;
}

void config_set_defaults(AgentConfig* config)
{
    if (!config) return;
    
    strcpy(config->server_host, "localhost");
    config->server_port = 3000;
    strcpy(config->agent_id, "agent-core-001");
    config->reconnect_interval = RECONNECT_INTERVAL_MS;
    config->max_reconnect_attempts = MAX_RECONNECT_ATTEMPTS;
    config->log_level = LOG_INFO;
}

AgentResult config_load_from_file(AgentConfig* config, const char* filename)
{
    if (!config || !filename) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    FILE* file = fopen(filename, "r");
    if (!file) {
        LOG_WARN_MSG("Config file not found, using defaults: %s", filename);
        config_set_defaults(config);
        return AGENT_SUCCESS;
    }
    
    char line[256];
    while (fgets(line, sizeof(line), file)) {
        // Remove newline
        line[strcspn(line, "\r\n")] = 0;
        
        // Skip empty lines and comments
        if (line[0] == 0 || line[0] == '#') {
            continue;
        }
        
        char* equals = strchr(line, '=');
        if (!equals) continue;
        
        *equals = 0;
        char* key = line;
        char* value = equals + 1;
        
        // Trim whitespace
        while (*key == ' ' || *key == '\t') key++;
        while (*value == ' ' || *value == '\t') value++;
        
        if (strcmp(key, "server_host") == 0) {
            strcpy(config->server_host, value);
        } else if (strcmp(key, "server_port") == 0) {
            config->server_port = atoi(value);
        } else if (strcmp(key, "agent_id") == 0) {
            strcpy(config->agent_id, value);
        } else if (strcmp(key, "log_level") == 0) {
            config->log_level = atoi(value);
        }
    }
    
    fclose(file);
    LOG_INFO_MSG("Configuration loaded from: %s", filename);
    return AGENT_SUCCESS;
}

AgentResult config_save_to_file(const AgentConfig* config, const char* filename)
{
    if (!config || !filename) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    FILE* file = fopen(filename, "w");
    if (!file) {
        LOG_ERROR_MSG("Failed to save config to: %s", filename);
        return AGENT_ERROR_SYSTEM;
    }
    
    fprintf(file, "# Agent Core Configuration (Unified Push Mode)\n");
    fprintf(file, "server_host=%s\n", config->server_host);
    fprintf(file, "server_port=%d\n", config->server_port);
    fprintf(file, "agent_id=%s\n", config->agent_id);
    fprintf(file, "log_level=%d\n", config->log_level);
    
    fclose(file);
    LOG_INFO_MSG("Configuration saved to: %s", filename);
    return AGENT_SUCCESS;
}