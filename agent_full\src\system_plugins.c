#include "agent.h"
#include "logger.h"
#include "utils.h"

// 心跳推送插件
typedef struct {
    uint64_t last_heartbeat;
    int heartbeat_interval;
#ifdef _WIN32
    HANDLE heartbeat_thread;
#else
    pthread_t heartbeat_thread;
#endif
    bool enabled;
    volatile bool running;
} HeartbeatPlugin;

// 系统信息推送插件
typedef struct {
    char last_system_info[1024];
    uint64_t last_check_time;
    int check_interval;
#ifdef _WIN32
    HANDLE monitor_thread;
#else
    pthread_t monitor_thread;
#endif
    bool enabled;
    volatile bool running;
} SystemInfoPlugin;

// 全局系统插件实例
static HeartbeatPlugin g_heartbeat_plugin = {0};
static SystemInfoPlugin g_system_info_plugin = {0};

// 心跳推送线程函数
#ifdef _WIN32
DWORD WINAPI heartbeat_push_thread(LPVOID param)
#else
void* heartbeat_push_thread(void* param)
#endif
{
    AgentContext* ctx = (AgentContext*)param;
    HeartbeatPlugin* plugin = &g_heartbeat_plugin;
    
    LOG_INFO_MSG("Heartbeat push thread started");
    
    while (plugin->running) {
        uint64_t now = get_timestamp_ms();
        
        if (now - plugin->last_heartbeat >= plugin->heartbeat_interval) {
            // 收集系统状态
            char heartbeat_data[512];
            snprintf(heartbeat_data, sizeof(heartbeat_data),
                "{"
                "\"status\":\"alive\","
                "\"timestamp\":%llu,"
                "\"cpu_usage\":%.2f,"
                "\"memory_usage\":%.2f,"
                "\"uptime\":%llu,"
                "\"active_plugins\":%d,"
                "\"messages_sent\":%llu,"
                "\"messages_received\":%llu"
                "}",
                now,
                ctx->stats.cpu_usage,
                (float)ctx->stats.memory_usage / (1024 * 1024), // MB
                now - ctx->stats.start_time,
                ctx->plugin_count,
                ctx->stats.messages_sent,
                ctx->stats.messages_received);
            
            // 推送心跳数据
            int result = agent_push_data("system", "heartbeat", heartbeat_data, 8);
            if (result == AGENT_SUCCESS) {
                plugin->last_heartbeat = now;
                LOG_DEBUG_MSG("Heartbeat pushed successfully");
            } else {
                LOG_WARN_MSG("Failed to push heartbeat: %d", result);
            }
        }
        
        SLEEP_MS(1000); // 每秒检查一次
    }
    
    LOG_INFO_MSG("Heartbeat push thread ended");
    
#ifdef _WIN32
    return 0;
#else
    return NULL;
#endif
}

// 系统信息监控线程函数
#ifdef _WIN32
DWORD WINAPI system_info_monitor_thread(LPVOID param)
#else
void* system_info_monitor_thread(void* param)
#endif
{
    AgentContext* ctx = (AgentContext*)param;
    SystemInfoPlugin* plugin = &g_system_info_plugin;
    
    LOG_INFO_MSG("System info monitor thread started");
    
    while (plugin->running) {
        uint64_t now = get_timestamp_ms();
        
        if (now - plugin->last_check_time >= plugin->check_interval) {
            char current_system_info[1024];
            get_system_info(current_system_info, sizeof(current_system_info));
            
            // 检查系统信息是否有变化
            if (strcmp(current_system_info, plugin->last_system_info) != 0) {
                // 推送系统信息变化
                int result = agent_push_data("system", "system_info", current_system_info, 7);
                if (result == AGENT_SUCCESS) {
                    strncpy(plugin->last_system_info, current_system_info, sizeof(plugin->last_system_info) - 1);
                    plugin->last_system_info[sizeof(plugin->last_system_info) - 1] = '\0';
                    LOG_DEBUG_MSG("System info pushed successfully");
                } else {
                    LOG_WARN_MSG("Failed to push system info: %d", result);
                }
            }
            
            plugin->last_check_time = now;
        }
        
        SLEEP_MS(5000); // 每5秒检查一次
    }
    
    LOG_INFO_MSG("System info monitor thread ended");
    
#ifdef _WIN32
    return 0;
#else
    return NULL;
#endif
}

// 启动心跳推送插件
AgentResult start_heartbeat_plugin(AgentContext* ctx, int interval_ms)
{
    if (!ctx || interval_ms <= 0) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    HeartbeatPlugin* plugin = &g_heartbeat_plugin;
    
    if (plugin->running) {
        return AGENT_SUCCESS; // 已经在运行
    }
    
    plugin->heartbeat_interval = interval_ms;
    plugin->last_heartbeat = 0;
    plugin->enabled = true;
    plugin->running = true;
    
    // 启动心跳线程
#ifdef _WIN32
    plugin->heartbeat_thread = CreateThread(NULL, 0, heartbeat_push_thread, ctx, 0, NULL);
    if (plugin->heartbeat_thread == NULL) {
        plugin->running = false;
        plugin->enabled = false;
        LOG_ERROR_MSG("Failed to create heartbeat thread");
        return AGENT_ERROR_SYSTEM;
    }
#else
    if (pthread_create(&plugin->heartbeat_thread, NULL, heartbeat_push_thread, ctx) != 0) {
        plugin->running = false;
        plugin->enabled = false;
        LOG_ERROR_MSG("Failed to create heartbeat thread");
        return AGENT_ERROR_SYSTEM;
    }
#endif
    
    LOG_INFO_MSG("Heartbeat plugin started with interval %d ms", interval_ms);
    return AGENT_SUCCESS;
}

// 停止心跳推送插件
void stop_heartbeat_plugin(void)
{
    HeartbeatPlugin* plugin = &g_heartbeat_plugin;
    
    if (!plugin->running) {
        return;
    }
    
    plugin->running = false;
    plugin->enabled = false;
    
    // 等待线程结束
#ifdef _WIN32
    if (plugin->heartbeat_thread != NULL) {
        WaitForSingleObject(plugin->heartbeat_thread, 5000);
        CloseHandle(plugin->heartbeat_thread);
        plugin->heartbeat_thread = NULL;
    }
#else
    if (plugin->heartbeat_thread != 0) {
        pthread_join(plugin->heartbeat_thread, NULL);
        plugin->heartbeat_thread = 0;
    }
#endif
    
    LOG_INFO_MSG("Heartbeat plugin stopped");
}

// 启动系统信息推送插件
AgentResult start_system_info_plugin(AgentContext* ctx, int check_interval_ms)
{
    if (!ctx || check_interval_ms <= 0) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    SystemInfoPlugin* plugin = &g_system_info_plugin;
    
    if (plugin->running) {
        return AGENT_SUCCESS; // 已经在运行
    }
    
    plugin->check_interval = check_interval_ms;
    plugin->last_check_time = 0;
    plugin->enabled = true;
    plugin->running = true;
    memset(plugin->last_system_info, 0, sizeof(plugin->last_system_info));
    
    // 启动监控线程
#ifdef _WIN32
    plugin->monitor_thread = CreateThread(NULL, 0, system_info_monitor_thread, ctx, 0, NULL);
    if (plugin->monitor_thread == NULL) {
        plugin->running = false;
        plugin->enabled = false;
        LOG_ERROR_MSG("Failed to create system info monitor thread");
        return AGENT_ERROR_SYSTEM;
    }
#else
    if (pthread_create(&plugin->monitor_thread, NULL, system_info_monitor_thread, ctx) != 0) {
        plugin->running = false;
        plugin->enabled = false;
        LOG_ERROR_MSG("Failed to create system info monitor thread");
        return AGENT_ERROR_SYSTEM;
    }
#endif
    
    LOG_INFO_MSG("System info plugin started with check interval %d ms", check_interval_ms);
    return AGENT_SUCCESS;
}

// 停止系统信息推送插件
void stop_system_info_plugin(void)
{
    SystemInfoPlugin* plugin = &g_system_info_plugin;
    
    if (!plugin->running) {
        return;
    }
    
    plugin->running = false;
    plugin->enabled = false;
    
    // 等待线程结束
#ifdef _WIN32
    if (plugin->monitor_thread != NULL) {
        WaitForSingleObject(plugin->monitor_thread, 5000);
        CloseHandle(plugin->monitor_thread);
        plugin->monitor_thread = NULL;
    }
#else
    if (plugin->monitor_thread != 0) {
        pthread_join(plugin->monitor_thread, NULL);
        plugin->monitor_thread = 0;
    }
#endif
    
    LOG_INFO_MSG("System info plugin stopped");
}

// 启动所有系统级推送插件
AgentResult start_system_push_plugins(AgentContext* ctx)
{
    if (!ctx) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    // 启动心跳插件（30秒间隔）
    AgentResult result = start_heartbeat_plugin(ctx, 30000);
    if (result != AGENT_SUCCESS) {
        LOG_ERROR_MSG("Failed to start heartbeat plugin");
        return result;
    }
    
    // 启动系统信息插件（10秒检查间隔）
    result = start_system_info_plugin(ctx, 10000);
    if (result != AGENT_SUCCESS) {
        LOG_ERROR_MSG("Failed to start system info plugin");
        stop_heartbeat_plugin();
        return result;
    }
    
    LOG_INFO_MSG("All system push plugins started");
    return AGENT_SUCCESS;
}

// 停止所有系统级推送插件
void stop_system_push_plugins(void)
{
    stop_heartbeat_plugin();
    stop_system_info_plugin();
    LOG_INFO_MSG("All system push plugins stopped");
}
