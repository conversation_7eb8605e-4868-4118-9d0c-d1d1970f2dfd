#include "message_handler.h"
#include "websocket_client.h"
#include "logger.h"
#include "utils.h"
#include "plugin_manager.h"

AgentResult message_handler_init(AgentContext* ctx)
{
    if (!ctx) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    LOG_INFO_MSG("Message handler initialized");
    return AGENT_SUCCESS;
}

void message_handler_cleanup(AgentContext* ctx)
{
    if (!ctx) {
        return;
    }
    
    LOG_INFO_MSG("Message handler cleanup completed");
}

AgentResult message_handler_process(AgentContext* ctx, const char* message)
{
    if (!ctx || !message) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    LOG_DEBUG_MSG("Processing message: %s", message);
    
    MessageType msg_type = parse_message_type(message);
    
    switch (msg_type) {
        case MSG_TYPE_HEARTBEAT:
            LOG_DEBUG_MSG("Received heartbeat request");
            return ws_client_send_heartbeat(ctx);
            
        case MSG_TYPE_SYSTEM_INFO:
            LOG_DEBUG_MSG("Received system info request");
            return ws_client_send_system_info(ctx);
            
        case MSG_TYPE_PLUGIN_DATA:
            LOG_DEBUG_MSG("Plugin data request ignored - using unified push mode");
            // 在统一推送模式下，插件主动推送数据，不再响应数据请求
            {
                char* response = create_json_message("plugin_data_info", ctx->config.agent_id,
                    "{\"message\":\"Agent is in unified push mode. Plugins actively push data.\"}");
                if (response) {
                    AgentResult result = ws_client_send_message(ctx, response);
                    free_json_message(response);
                    return result;
                }
            }
            break;
            
        case MSG_TYPE_STATUS:
            LOG_DEBUG_MSG("Received status request");
            return message_handler_send_status(ctx);
            
        case MSG_TYPE_COMMAND:
            {
                char* command = extract_json_field(message, "command");
                char* params = extract_json_field(message, "params");
                
                AgentResult result = message_handler_handle_command(ctx, command, params);
                
                if (command) free(command);
                if (params) free(params);
                
                return result;
            }
            
        default:
            LOG_WARN_MSG("Unknown message type received");
            break;
    }
    
    return AGENT_SUCCESS;
}

AgentResult message_handler_send_status(AgentContext* ctx)
{
    if (!ctx) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    char status_buffer[2048];
    snprintf(status_buffer, sizeof(status_buffer),
        "{"
        "\"agent_id\":\"%s\","
        "\"state\":\"%s\","
        "\"uptime\":%llu,"
        "\"plugin_count\":%d,"
        "\"memory_usage\":%lu,"
        "\"cpu_usage\":%.2f"
        "}",
        ctx->config.agent_id,
        (ctx->state == AGENT_STATE_CONNECTED) ? "connected" : "disconnected",
        get_timestamp_ms() - ctx->stats.start_time,
        ctx->plugin_count,
        ctx->stats.memory_usage,
        ctx->stats.cpu_usage);
    
    char* message = create_json_message("status", ctx->config.agent_id, status_buffer);
    if (!message) {
        return AGENT_ERROR_MEMORY;
    }
    
    AgentResult result = ws_client_send_message(ctx, message);
    free_json_message(message);
    
    return result;
}

AgentResult message_handler_handle_command(AgentContext* ctx, const char* command, const char* params)
{
    if (!ctx || !command) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    LOG_INFO_MSG("Handling command: %s", command);
    
    if (strcmp(command, "reload_plugins") == 0) {
        return plugin_manager_scan_directory(ctx);
    }
    else if (strcmp(command, "get_plugin_status") == 0) {
        char status_buffer[2048];
        plugin_manager_get_status(ctx, status_buffer, sizeof(status_buffer));
        
        char* message = create_json_message("plugin_status", ctx->config.agent_id, status_buffer);
        if (message) {
            AgentResult result = ws_client_send_message(ctx, message);
            free_json_message(message);
            return result;
        }
        return AGENT_ERROR_MEMORY;
    }
    else if (strcmp(command, "shutdown") == 0) {
        LOG_INFO_MSG("Shutdown command received");
        ctx->state = AGENT_STATE_SHUTDOWN;
        return AGENT_SUCCESS;
    }
    else {
        LOG_WARN_MSG("Unknown command: %s", command);
        return AGENT_ERROR_INVALID_PARAM;
    }
}

MessageType parse_message_type(const char* message)
{
    if (!message) {
        return MSG_TYPE_UNKNOWN;
    }
    
    char* type = extract_json_field(message, "type");
    if (!type) {
        return MSG_TYPE_UNKNOWN;
    }
    
    MessageType result = MSG_TYPE_UNKNOWN;
    
    if (strcmp(type, "heartbeat") == 0) {
        result = MSG_TYPE_HEARTBEAT;
    } else if (strcmp(type, "system_info") == 0) {
        result = MSG_TYPE_SYSTEM_INFO;
    } else if (strcmp(type, "plugin_data") == 0) {
        result = MSG_TYPE_PLUGIN_DATA;
    } else if (strcmp(type, "status") == 0) {
        result = MSG_TYPE_STATUS;
    } else if (strcmp(type, "command") == 0) {
        result = MSG_TYPE_COMMAND;
    } else if (strcmp(type, "error") == 0) {
        result = MSG_TYPE_ERROR;
    }
    
    free(type);
    return result;
}

char* extract_json_field(const char* json, const char* field)
{
    if (!json || !field) {
        return NULL;
    }
    
    char search_pattern[128];
    snprintf(search_pattern, sizeof(search_pattern), "\"%s\":\"", field);
    
    char* start = strstr(json, search_pattern);
    if (!start) {
        return NULL;
    }
    
    start += strlen(search_pattern);
    char* end = strchr(start, '"');
    if (!end) {
        return NULL;
    }
    
    size_t length = end - start;
    char* result = malloc(length + 1);
    if (!result) {
        return NULL;
    }
    
    memcpy(result, start, length);
    result[length] = '\0';
    
    return result;
}