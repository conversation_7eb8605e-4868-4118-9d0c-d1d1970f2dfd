cmake_minimum_required(VERSION 3.16)
project(CameraMonitorPush VERSION 2.0.0 LANGUAGES C)

# Set C standard
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler options
if(MSVC)
    add_compile_options(/W3 /utf-8)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra)
endif()

# Source files
set(PLUGIN_SOURCES
    src/camera_plugin_push.c
)

# Platform specific libraries
if(WIN32)
    set(PLATFORM_LIBS user32 kernel32)
else()
    set(PLATFORM_LIBS pthread)
endif()

# Create shared library
add_library(camera_monitor_push SHARED ${PLUGIN_SOURCES})

# Link libraries
target_link_libraries(camera_monitor_push ${PLATFORM_LIBS})

# Properties
set_target_properties(camera_monitor_push PROPERTIES
    PREFIX ""
    OUTPUT_NAME "camera_monitor_push"
    DEBUG_POSTFIX "_d"
)

# Windows specific settings
if(WIN32)
    set_target_properties(camera_monitor_push PROPERTIES
        SUFFIX ".dll"
    )
else()
    set_target_properties(camera_monitor_push PROPERTIES
        SUFFIX ".so"
    )
endif()

# Install
install(TARGETS camera_monitor_push
    LIBRARY DESTINATION plugins
    RUNTIME DESTINATION plugins
)
