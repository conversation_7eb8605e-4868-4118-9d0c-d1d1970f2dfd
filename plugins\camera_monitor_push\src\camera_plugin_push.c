#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <time.h>

#ifdef _WIN32
#include <windows.h>
#define SLEEP_MS(ms) Sleep(ms)
#else
#include <unistd.h>
#include <pthread.h>
#define SLEEP_MS(ms) usleep((ms) * 1000)
#endif

// 函数声明
__declspec(dllexport) int plugin_stop_push_mode(void);

// 插件状态
typedef struct {
    bool initialized;
    bool push_mode_enabled;
    volatile bool running;
    int capture_interval;
    uint64_t frame_count;
    char last_error[256];
    
    // 推送回调函数
    int (*push_callback)(const char* source_name, const char* data_type, const char* data, int priority);
    
    // 推送线程
#ifdef _WIN32
    HANDLE push_thread;
#else
    pthread_t push_thread;
#endif
} CameraPluginState;

static CameraPluginState g_plugin_state = {0};

// 获取当前时间戳（毫秒）
static uint64_t get_timestamp_ms(void)
{
#ifdef _WIN32
    return GetTickCount64();
#else
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint64_t)(tv.tv_sec * 1000 + tv.tv_usec / 1000);
#endif
}

// 模拟摄像头数据捕获
static char* capture_camera_frame(void)
{
    // 模拟摄像头数据
    static int frame_id = 0;
    frame_id++;
    
    uint64_t timestamp = get_timestamp_ms();
    
    // 创建JSON格式的摄像头数据
    char* frame_data = malloc(512);
    if (!frame_data) {
        return NULL;
    }
    
    snprintf(frame_data, 512,
        "{"
        "\"frameId\":%d,"
        "\"timestamp\":%llu,"
        "\"resolution\":\"1920x1080\","
        "\"format\":\"MJPEG\","
        "\"size\":1048576,"
        "\"quality\":85,"
        "\"motion_detected\":%s,"
        "\"brightness\":%.2f,"
        "\"contrast\":%.2f"
        "}",
        frame_id,
        timestamp,
        (frame_id % 10 == 0) ? "true" : "false", // 每10帧检测到运动
        50.0 + (rand() % 100) / 10.0,  // 随机亮度
        1.0 + (rand() % 50) / 100.0    // 随机对比度
    );
    
    return frame_data;
}

// 推送线程函数
#ifdef _WIN32
DWORD WINAPI camera_push_thread(LPVOID param)
#else
void* camera_push_thread(void* param)
#endif
{
    CameraPluginState* state = (CameraPluginState*)param;
    
    printf("[CameraPush] Push thread started\n");
    
    while (state->running && state->push_mode_enabled) {
        // 捕获摄像头帧
        char* frame_data = capture_camera_frame();
        if (frame_data) {
            // 推送数据到Agent
            if (state->push_callback) {
                int priority = (strstr(frame_data, "\"motion_detected\":true")) ? 9 : 5; // 检测到运动时高优先级
                int result = state->push_callback("camera_monitor_push", "camera_frame", frame_data, priority);
                
                if (result == 0) {
                    state->frame_count++;
                    printf("[CameraPush] Frame %llu pushed (priority=%d)\n", state->frame_count, priority);
                } else {
                    printf("[CameraPush] Failed to push frame: %d\n", result);
                }
            }
            
            free(frame_data);
        } else {
            printf("[CameraPush] Failed to capture frame\n");
        }
        
        // 等待下一次捕获
        SLEEP_MS(state->capture_interval);
    }
    
    printf("[CameraPush] Push thread ended\n");
    
#ifdef _WIN32
    return 0;
#else
    return NULL;
#endif
}

// 插件初始化
__declspec(dllexport) int plugin_init(void* agent_context)
{
    printf("[CameraPush] Initializing camera push plugin...\n");
    
    memset(&g_plugin_state, 0, sizeof(g_plugin_state));
    g_plugin_state.capture_interval = 1000; // 1秒间隔
    g_plugin_state.initialized = true;
    
    printf("[CameraPush] Camera push plugin initialized\n");
    return 0;
}

// 插件清理
__declspec(dllexport) int plugin_cleanup(void)
{
    printf("[CameraPush] Cleaning up camera push plugin...\n");
    
    // 停止推送模式
    if (g_plugin_state.push_mode_enabled) {
        plugin_stop_push_mode();
    }
    
    g_plugin_state.initialized = false;
    
    printf("[CameraPush] Camera push plugin cleanup completed\n");
    return 0;
}

// 注册推送回调
__declspec(dllexport) int plugin_register_push_callback(int (*push_callback)(const char* source_name,
                                                                             const char* data_type,
                                                                             const char* data,
                                                                             int priority))
{
    if (!push_callback) {
        return -1;
    }
    
    g_plugin_state.push_callback = push_callback;
    printf("[CameraPush] Push callback registered\n");
    return 0;
}

// 启动推送模式
__declspec(dllexport) int plugin_start_push_mode(void)
{
    if (!g_plugin_state.initialized) {
        return -1;
    }
    
    if (g_plugin_state.push_mode_enabled) {
        return 0; // 已经启动
    }
    
    g_plugin_state.push_mode_enabled = true;
    g_plugin_state.running = true;
    g_plugin_state.frame_count = 0;
    
    // 启动推送线程
#ifdef _WIN32
    g_plugin_state.push_thread = CreateThread(NULL, 0, camera_push_thread, &g_plugin_state, 0, NULL);
    if (g_plugin_state.push_thread == NULL) {
        g_plugin_state.push_mode_enabled = false;
        g_plugin_state.running = false;
        printf("[CameraPush] Failed to create push thread\n");
        return -1;
    }
#else
    if (pthread_create(&g_plugin_state.push_thread, NULL, camera_push_thread, &g_plugin_state) != 0) {
        g_plugin_state.push_mode_enabled = false;
        g_plugin_state.running = false;
        printf("[CameraPush] Failed to create push thread\n");
        return -1;
    }
#endif
    
    printf("[CameraPush] Push mode started with %d ms interval\n", g_plugin_state.capture_interval);
    return 0;
}

// 停止推送模式
__declspec(dllexport) int plugin_stop_push_mode(void)
{
    if (!g_plugin_state.push_mode_enabled) {
        return 0;
    }
    
    g_plugin_state.running = false;
    g_plugin_state.push_mode_enabled = false;
    
    // 等待推送线程结束
#ifdef _WIN32
    if (g_plugin_state.push_thread != NULL) {
        WaitForSingleObject(g_plugin_state.push_thread, 5000);
        CloseHandle(g_plugin_state.push_thread);
        g_plugin_state.push_thread = NULL;
    }
#else
    if (g_plugin_state.push_thread != 0) {
        pthread_join(g_plugin_state.push_thread, NULL);
        g_plugin_state.push_thread = 0;
    }
#endif
    
    printf("[CameraPush] Push mode stopped. Total frames pushed: %llu\n", g_plugin_state.frame_count);
    return 0;
}

// 配置推送参数
__declspec(dllexport) int plugin_configure_push(const char* config_json)
{
    if (!config_json) {
        return -1;
    }
    
    // 简单的配置解析（实际应该使用JSON解析库）
    if (strstr(config_json, "\"interval\":")) {
        // 提取间隔值
        const char* interval_str = strstr(config_json, "\"interval\":");
        if (interval_str) {
            int interval = atoi(interval_str + 11);
            if (interval > 0 && interval <= 60000) {
                g_plugin_state.capture_interval = interval;
                printf("[CameraPush] Capture interval updated to %d ms\n", interval);
                return 0;
            }
        }
    }
    
    printf("[CameraPush] Invalid configuration: %s\n", config_json);
    return -1;
}

// 获取插件信息
__declspec(dllexport) const char* plugin_get_plugin_info(void)
{
    static char info[512];
    snprintf(info, sizeof(info),
        "{"
        "\"name\":\"camera_monitor_push\","
        "\"version\":\"2.0.0\","
        "\"description\":\"Camera monitoring plugin with unified push mode\","
        "\"author\":\"Agent System\","
        "\"push_mode_supported\":true,"
        "\"initialized\":%s,"
        "\"push_enabled\":%s,"
        "\"frames_captured\":%llu,"
        "\"capture_interval\":%d"
        "}",
        g_plugin_state.initialized ? "true" : "false",
        g_plugin_state.push_mode_enabled ? "true" : "false",
        g_plugin_state.frame_count,
        g_plugin_state.capture_interval);
    
    return info;
}
