#include "agent.h"
#include "websocket_client.h"
#include "plugin_manager.h"
#include "message_handler.h"
#include "logger.h"
#include "utils.h"
#include "config.h"

#include <signal.h>

static volatile bool g_running = true;
static AgentContext* g_ctx = NULL;

void signal_handler(int sig)
{
    LOG_INFO_MSG("Received signal %d, shutting down...", sig);
    g_running = false;
    if (g_ctx) {
        g_ctx->state = AGENT_STATE_SHUTDOWN;
    }
}

void setup_signal_handlers(void)
{
#ifdef _WIN32
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
#else
    struct sigaction sa;
    sa.sa_handler = signal_handler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0;
    
    sigaction(SIGINT, &sa, NULL);
    sigaction(SIGTERM, &sa, NULL);
#endif
}

AgentResult initialize_agent(AgentContext* ctx)
{
    LOG_INFO_MSG("Initializing agent...");
    
    // Initialize context
    memset(ctx, 0, sizeof(AgentContext));
    ctx->websocket = INVALID_SOCKET;
    ctx->state = AGENT_STATE_DISCONNECTED;
    ctx->stats.start_time = get_timestamp_ms();
    
    // Load default configuration
    config_set_defaults(&ctx->config);

    // 统一推送配置
    ctx->config.push_enabled = true;
    ctx->config.push_queue_size = MAX_PUSH_QUEUE_SIZE;
    ctx->config.push_thread_count = 2;
    ctx->config.max_retry_count = MAX_RETRY_COUNT;
    ctx->config.push_timeout_ms = DEFAULT_PUSH_TIMEOUT_MS;
    ctx->config.enable_data_compression = false;
    
    // Initialize components
    AgentResult result;
    
    result = logger_init(LOG_INFO);
    if (result != AGENT_SUCCESS) {
        printf("Failed to initialize logger\n");
        return result;
    }
    
    result = ws_client_init();
    if (result != AGENT_SUCCESS) {
        LOG_ERROR_MSG("Failed to initialize WebSocket client");
        return result;
    }
    
    result = plugin_manager_init(ctx);
    if (result != AGENT_SUCCESS) {
        LOG_ERROR_MSG("Failed to initialize plugin manager");
        return result;
    }
    
    result = message_handler_init(ctx);
    if (result != AGENT_SUCCESS) {
        LOG_ERROR_MSG("Failed to initialize message handler");
        return result;
    }

    // 初始化统一推送管理器
    result = unified_push_manager_init(&ctx->push_manager,
                                      ctx->config.push_queue_size,
                                      ctx->config.push_thread_count);
    if (result != AGENT_SUCCESS) {
        LOG_ERROR_MSG("Failed to initialize unified push manager");
        return result;
    }

    // 注册全局Agent上下文
    result = agent_register_global_context(ctx);
    if (result != AGENT_SUCCESS) {
        LOG_ERROR_MSG("Failed to register global agent context");
        return result;
    }

    LOG_INFO_MSG("Agent initialized successfully");
    return AGENT_SUCCESS;
}

void cleanup_agent(AgentContext* ctx)
{
    if (!ctx) return;

    LOG_INFO_MSG("Cleaning up agent...");

    // 停止所有系统级推送插件
    stop_system_push_plugins();

    // 停止所有业务插件的推送模式
    stop_all_plugin_push_mode(ctx);

    // 停止统一推送管理器
    unified_push_manager_cleanup(&ctx->push_manager);

    // Disconnect from server
    if (ws_client_is_connected(ctx)) {
        ws_client_disconnect(ctx);
    }

    // Cleanup components
    message_handler_cleanup(ctx);
    plugin_manager_cleanup(ctx);
    ws_client_cleanup();
    logger_cleanup();

    LOG_INFO_MSG("Agent cleanup completed");
}

AgentResult main_loop(AgentContext* ctx)
{
    LOG_INFO_MSG("Starting unified push mode main loop...");

    // 初始化统一推送管理器
    AgentResult result = unified_push_manager_start(&ctx->push_manager, ctx);
    if (result != AGENT_SUCCESS) {
        LOG_ERROR_MSG("Failed to start unified push manager");
        return result;
    }

    // 启动系统级推送插件
    result = start_system_push_plugins(ctx);
    if (result != AGENT_SUCCESS) {
        LOG_ERROR_MSG("Failed to start system push plugins");
        unified_push_manager_stop(&ctx->push_manager);
        return result;
    }

    // 启动所有业务插件的推送模式
    result = start_all_plugin_push_mode(ctx);
    if (result != AGENT_SUCCESS) {
        LOG_ERROR_MSG("Failed to start plugin push mode");
        stop_system_push_plugins();
        unified_push_manager_stop(&ctx->push_manager);
        return result;
    }

    while (g_running && ctx->state != AGENT_STATE_SHUTDOWN) {
        uint64_t now = get_timestamp_ms();

        // Try to connect if not connected
        if (!ws_client_is_connected(ctx)) {
            if (ctx->reconnect_count < ctx->config.max_reconnect_attempts) {
                LOG_INFO_MSG("Attempting to connect to server... (attempt %d/%d)",
                           ctx->reconnect_count + 1, ctx->config.max_reconnect_attempts);

                AgentResult connect_result = ws_client_connect(ctx);
                if (connect_result == AGENT_SUCCESS) {
                    LOG_INFO_MSG("Connected to server, resuming push mode");
                    ctx->reconnect_count = 0;
                    unified_push_manager_resume(&ctx->push_manager);
                } else {
                    ctx->reconnect_count++;
                    LOG_WARN_MSG("Connection failed, will retry in %d ms",
                               ctx->config.reconnect_interval);

                    SLEEP_MS(ctx->config.reconnect_interval);
                    continue;
                }
            } else {
                LOG_ERROR_MSG("Max reconnection attempts reached, shutting down");
                break;
            }
        }

        // 处理服务器消息（主要是配置和控制消息）
        char message_buffer[2048];
        size_t received_size;

        AgentResult msg_result = ws_client_receive_message(ctx, message_buffer,
                                                         sizeof(message_buffer), &received_size);

        if (msg_result == AGENT_SUCCESS && received_size > 0) {
            // 处理控制消息（非数据消息）
            message_handler_process_control(ctx, message_buffer);
        } else if (msg_result == AGENT_ERROR_NETWORK) {
            LOG_WARN_MSG("Network error, connection lost");
            ctx->state = AGENT_STATE_DISCONNECTED;
            unified_push_manager_pause(&ctx->push_manager);
        }

        // 更新统计信息
        ctx->stats.uptime = now - ctx->stats.start_time;

        // 减少CPU占用
        SLEEP_MS(100);
    }

    // 清理
    stop_all_plugin_push_mode(ctx);
    stop_system_push_plugins();
    unified_push_manager_stop(&ctx->push_manager);

    LOG_INFO_MSG("Unified push mode main loop ended");
    return AGENT_SUCCESS;
}

int main(int argc, char* argv[])
{
    printf("Agent Full v1.0 - Distributed Monitoring System\n");
    printf("Starting agent...\n");
    
    // Setup signal handlers
    setup_signal_handlers();
    
    AgentContext ctx;
    g_ctx = &ctx;
    
    // Initialize agent
    AgentResult result = initialize_agent(&ctx);
    if (result != AGENT_SUCCESS) {
        printf("Failed to initialize agent: %d\n", result);
        return 1;
    }

    // Load configuration from file if provided
    if (argc > 1) {
        result = config_load_from_file(&ctx.config, argv[1]);
        if (result != AGENT_SUCCESS) {
            printf("Failed to load configuration: %d\n", result);
            return 1;
        }
        LOG_INFO_MSG("Configuration loaded: server=%s:%d, agent_id=%s",
                    ctx.config.server_host, ctx.config.server_port, ctx.config.agent_id);
    }
    
    // Run main loop
    result = main_loop(&ctx);
    
    // Cleanup
    cleanup_agent(&ctx);
    
    printf("Agent terminated with code: %d\n", result);
    return (result == AGENT_SUCCESS) ? 0 : 1;
}