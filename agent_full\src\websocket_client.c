#include "websocket_client.h"
#include "logger.h"
#include "utils.h"

static bool g_winsock_initialized = false;

AgentResult ws_client_init(void)
{
#ifdef _WIN32
    if (!g_winsock_initialized) {
        WSADATA wsaData;
        int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
        if (result != 0) {
            LOG_ERROR_MSG("WSAStartup failed: %d", result);
            return AGENT_ERROR_NETWORK;
        }
        g_winsock_initialized = true;
    }
#endif
    
    LOG_INFO_MSG("WebSocket client initialized");
    return AGENT_SUCCESS;
}

void ws_client_cleanup(void)
{
#ifdef _WIN32
    if (g_winsock_initialized) {
        WSACleanup();
        g_winsock_initialized = false;
    }
#endif
    
    LOG_INFO_MSG("WebSocket client cleanup");
}

AgentResult ws_client_connect(AgentContext* ctx)
{
    if (!ctx) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    // Create socket
    ctx->websocket = socket(AF_INET, SOCK_STREAM, 0);
    if (ctx->websocket == INVALID_SOCKET) {
        LOG_ERROR_MSG("Failed to create socket: %d", GET_LAST_ERROR());
        return AGENT_ERROR_NETWORK;
    }
    
    // Set up server address
    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(ctx->config.server_port);
    
    // Convert hostname to IP
    struct hostent* he = gethostbyname(ctx->config.server_host);
    if (!he) {
        LOG_ERROR_MSG("Failed to resolve host: %s", ctx->config.server_host);
        closesocket(ctx->websocket);
        ctx->websocket = INVALID_SOCKET;
        return AGENT_ERROR_NETWORK;
    }
    
    memcpy(&server_addr.sin_addr, he->h_addr_list[0], he->h_length);
    
    // Connect to server
    if (connect(ctx->websocket, (struct sockaddr*)&server_addr, sizeof(server_addr)) == SOCKET_ERROR) {
        LOG_ERROR_MSG("Failed to connect to %s:%d - Error: %d", 
                     ctx->config.server_host, ctx->config.server_port, GET_LAST_ERROR());
        closesocket(ctx->websocket);
        ctx->websocket = INVALID_SOCKET;
        return AGENT_ERROR_NETWORK;
    }
    
    // Send WebSocket handshake
    char handshake[512];
    snprintf(handshake, sizeof(handshake),
        "GET / HTTP/1.1\r\n"
        "Host: %s:%d\r\n"
        "Upgrade: websocket\r\n"
        "Connection: Upgrade\r\n"
        "Sec-WebSocket-Key: dGhlIHNhbXBsZSBub25jZQ==\r\n"
        "Sec-WebSocket-Version: 13\r\n"
        "\r\n",
        ctx->config.server_host, ctx->config.server_port);
    
    if (send(ctx->websocket, handshake, strlen(handshake), 0) == SOCKET_ERROR) {
        LOG_ERROR_MSG("Failed to send handshake: %d", GET_LAST_ERROR());
        closesocket(ctx->websocket);
        ctx->websocket = INVALID_SOCKET;
        return AGENT_ERROR_WEBSOCKET;
    }
    
    // Receive handshake response
    char response[1024];
    int received = recv(ctx->websocket, response, sizeof(response) - 1, 0);
    if (received <= 0) {
        LOG_ERROR_MSG("Failed to receive handshake response: %d", GET_LAST_ERROR());
        closesocket(ctx->websocket);
        ctx->websocket = INVALID_SOCKET;
        return AGENT_ERROR_WEBSOCKET;
    }
    
    response[received] = '\0';
    
    // Check if handshake was successful
    if (strstr(response, "HTTP/1.1 101") == NULL) {
        LOG_ERROR_MSG("WebSocket handshake failed");
        closesocket(ctx->websocket);
        ctx->websocket = INVALID_SOCKET;
        return AGENT_ERROR_WEBSOCKET;
    }
    
    ctx->state = AGENT_STATE_CONNECTED;
    ctx->reconnect_count = 0;
    
    LOG_INFO_MSG("Connected to WebSocket server: %s:%d", 
                ctx->config.server_host, ctx->config.server_port);
    
    return AGENT_SUCCESS;
}

AgentResult ws_client_disconnect(AgentContext* ctx)
{
    if (!ctx || ctx->websocket == INVALID_SOCKET) {
        return AGENT_SUCCESS;
    }
    
    closesocket(ctx->websocket);
    ctx->websocket = INVALID_SOCKET;
    ctx->state = AGENT_STATE_DISCONNECTED;
    
    LOG_INFO_MSG("Disconnected from WebSocket server");
    return AGENT_SUCCESS;
}

AgentResult ws_client_send_message(AgentContext* ctx, const char* message)
{
    if (!ctx || !message || ctx->websocket == INVALID_SOCKET) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    size_t msg_len = strlen(message);
    if (msg_len > 1024) {
        LOG_ERROR_MSG("Message too long: %zu bytes", msg_len);
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    // Create WebSocket frame
    uint8_t frame[2048];
    frame[0] = 0x81; // FIN=1, Opcode=1 (text frame)
    
    size_t frame_offset = 2;
    
    // Set payload length
    if (msg_len <= 125) {
        frame[1] = 0x80 | (uint8_t)msg_len; // MASK=1, Payload length
    } else if (msg_len <= 65535) {
        frame[1] = 0x80 | 126; // MASK=1, Extended payload length (16-bit)
        frame[2] = (msg_len >> 8) & 0xFF;
        frame[3] = msg_len & 0xFF;
        frame_offset = 4;
    } else {
        LOG_ERROR_MSG("Message too long for this implementation: %zu bytes", msg_len);
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    // Add mask
    uint32_t mask = 0x12345678;
    frame[frame_offset] = (mask >> 24) & 0xFF;
    frame[frame_offset + 1] = (mask >> 16) & 0xFF;
    frame[frame_offset + 2] = (mask >> 8) & 0xFF;
    frame[frame_offset + 3] = mask & 0xFF;
    frame_offset += 4;
    
    // Apply mask to message
    for (size_t i = 0; i < msg_len; i++) {
        frame[frame_offset + i] = message[i] ^ frame[frame_offset - 4 + (i % 4)];
    }
    
    size_t frame_size = frame_offset + msg_len;
    if (send(ctx->websocket, (char*)frame, frame_size, 0) == SOCKET_ERROR) {
        LOG_ERROR_MSG("Failed to send message: %d", GET_LAST_ERROR());
        return AGENT_ERROR_NETWORK;
    }
    
    LOG_DEBUG_MSG("Sent message: %zu bytes", msg_len);
    return AGENT_SUCCESS;
}

AgentResult ws_client_receive_message(AgentContext* ctx, char* buffer, size_t buffer_size, size_t* received_size)
{
    if (!ctx || !buffer || !received_size) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    if (ctx->websocket == INVALID_SOCKET) {
        return AGENT_ERROR_NETWORK;
    }
    
    // Simplified receive (for demo purposes)
    int received = recv(ctx->websocket, buffer, buffer_size - 1, 0);
    if (received <= 0) {
        if (received == 0) {
            LOG_INFO_MSG("Server closed connection");
            ctx->state = AGENT_STATE_DISCONNECTED;
            return AGENT_ERROR_NETWORK;
        } else {
            LOG_ERROR_MSG("Failed to receive message: %d", GET_LAST_ERROR());
            return AGENT_ERROR_NETWORK;
        }
    }
    
    buffer[received] = '\0';
    *received_size = received;
    
    LOG_DEBUG_MSG("Received message: %d bytes", received);
    return AGENT_SUCCESS;
}

bool ws_client_is_connected(const AgentContext* ctx)
{
    return ctx && ctx->state == AGENT_STATE_CONNECTED && ctx->websocket != INVALID_SOCKET;
}

AgentResult ws_client_send_heartbeat(AgentContext* ctx)
{
    if (!ctx) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    // Create heartbeat data with system metrics
    char heartbeat_data[256];
    snprintf(heartbeat_data, sizeof(heartbeat_data),
        "{"
        "\"status\":\"alive\","
        "\"systemLoad\":%.2f,"
        "\"memoryUsage\":%.2f,"
        "\"cpuUsage\":%.2f,"
        "\"activePlugins\":%d"
        "}",
        ctx->stats.cpu_usage,
        ctx->stats.memory_usage / 1024.0 / 1024.0, // Convert to MB
        ctx->stats.cpu_usage,
        ctx->plugin_count);
    
    char* message = create_json_message("heartbeat", ctx->config.agent_id, heartbeat_data);
    if (!message) {
        return AGENT_ERROR_MEMORY;
    }
    
    AgentResult result = ws_client_send_message(ctx, message);
    free_json_message(message);
    
    if (result == AGENT_SUCCESS) {
        LOG_DEBUG_MSG("Heartbeat sent");
    }
    
    return result;
}

AgentResult ws_client_send_system_info(AgentContext* ctx)
{
    if (!ctx) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    char sys_info[512];
    get_system_info(sys_info, sizeof(sys_info));
    
    char* message = create_json_message("system_info", ctx->config.agent_id, sys_info);
    if (!message) {
        return AGENT_ERROR_MEMORY;
    }
    
    AgentResult result = ws_client_send_message(ctx, message);
    free_json_message(message);
    
    if (result == AGENT_SUCCESS) {
        LOG_INFO_MSG("System info sent");
    }
    
    return result;
}