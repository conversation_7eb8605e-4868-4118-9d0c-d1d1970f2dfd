#include "utils.h"
#include "agent.h"
#include "websocket_client.h"
#include "logger.h"
#include <sys/stat.h>

#ifdef _WIN32
#include <windows.h>
#else
#include <sys/time.h>
#endif

uint64_t get_timestamp_ms(void)
{
#ifdef _WIN32
    return GetTickCount64();
#else
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint64_t)(tv.tv_sec * 1000 + tv.tv_usec / 1000);
#endif
}

void format_timestamp(char* buffer, size_t size)
{
    if (!buffer || size == 0) return;
    
    time_t now = time(NULL);
    struct tm* tm_info = localtime(&now);
    
    strftime(buffer, size, "%Y-%m-%d %H:%M:%S", tm_info);
}

char* create_json_message(const char* type, const char* agent_id, const char* data)
{
    if (!type || !agent_id) return NULL;
    
    size_t msg_size = 512 + (data ? strlen(data) : 0);
    char* message = malloc(msg_size);
    if (!message) return NULL;
    
    // Generate unique message ID
    char message_id[64];
    snprintf(message_id, sizeof(message_id), "%s-%llu", agent_id, get_timestamp_ms());
    
    // Get timestamp as number (milliseconds since epoch)
    uint64_t timestamp_ms = get_timestamp_ms();
    
    if (data) {
        snprintf(message, msg_size,
            "{"
            "\"type\":\"%s\","
            "\"timestamp\":%llu,"
            "\"agentId\":\"%s\","
            "\"messageId\":\"%s\","
            "\"data\":%s"
            "}", type, timestamp_ms, agent_id, message_id, data);
    } else {
        snprintf(message, msg_size,
            "{"
            "\"type\":\"%s\","
            "\"timestamp\":%llu,"
            "\"agentId\":\"%s\","
            "\"messageId\":\"%s\","
            "\"data\":{}"
            "}", type, timestamp_ms, agent_id, message_id);
    }
    
    return message;
}

void free_json_message(char* message)
{
    if (message) {
        free(message);
    }
}

void get_system_info(char* buffer, size_t size)
{
    if (!buffer || size == 0) return;
    
    char hostname[MAX_HOSTNAME_LEN] = "unknown";
    
#ifdef _WIN32
    DWORD hostname_size = sizeof(hostname);
    GetComputerNameA(hostname, &hostname_size);
    
    SYSTEM_INFO sys_info;
    GetSystemInfo(&sys_info);
    
    MEMORYSTATUSEX mem_info;
    mem_info.dwLength = sizeof(mem_info);
    GlobalMemoryStatusEx(&mem_info);
    
    snprintf(buffer, size,
        "{"
        "\"hostname\":\"%s\","
        "\"platform\":\"windows\","
        "\"cpus\":%lu,"
        "\"totalMemory\":%llu,"
        "\"freeMemory\":%llu"
        "}",
        hostname,
        sys_info.dwNumberOfProcessors,
        mem_info.ullTotalPhys,
        mem_info.ullAvailPhys);
#else
    gethostname(hostname, sizeof(hostname));
    
    snprintf(buffer, size,
        "{"
        "\"hostname\":\"%s\","
        "\"platform\":\"unix\","
        "\"cpus\":1,"
        "\"totalMemory\":0,"
        "\"freeMemory\":0"
        "}",
        hostname);
#endif
}

// 发送推送消息到服务器
AgentResult send_push_message_to_server(AgentContext* ctx, const UnifiedPushData* push_data)
{
    if (!ctx || !push_data) {
        return AGENT_ERROR_INVALID_PARAM;
    }

    if (!ws_client_is_connected(ctx)) {
        return AGENT_ERROR_NETWORK;
    }

    // 创建统一推送消息JSON
    size_t json_size = 1024 + (push_data->data ? strlen(push_data->data) : 0);
    char* json_message = malloc(json_size);
    if (!json_message) {
        return AGENT_ERROR_MEMORY;
    }

    // 构建推送消息JSON
    snprintf(json_message, json_size,
        "{"
        "\"type\":\"unified_push\","
        "\"timestamp\":%llu,"
        "\"agentId\":\"%s\","
        "\"messageId\":\"%s\","
        "\"data\":{"
            "\"sourceType\":\"%s\","
            "\"sourceName\":\"%s\","
            "\"dataType\":\"%s\","
            "\"payload\":%s,"
            "\"priority\":%d,"
            "\"messageId\":\"%s\""
        "}"
        "}",
        push_data->timestamp,
        ctx->config.agent_id,
        push_data->message_id,
        push_data->source_type,
        push_data->source_name,
        push_data->data_type,
        push_data->data ? push_data->data : "{}",
        push_data->priority,
        push_data->message_id);

    // 发送消息
    AgentResult result = ws_client_send_message(ctx, json_message);

    if (result == AGENT_SUCCESS) {
        ctx->stats.messages_sent++;
        ctx->stats.bytes_sent += strlen(json_message);
        LOG_DEBUG_MSG("Push message sent: %s:%s (priority=%d)",
                     push_data->source_name, push_data->data_type, push_data->priority);
    } else {
        LOG_ERROR_MSG("Failed to send push message: %s:%s",
                     push_data->source_name, push_data->data_type);
    }

    free(json_message);
    return result;
}