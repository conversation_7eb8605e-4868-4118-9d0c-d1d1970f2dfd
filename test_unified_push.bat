@echo off
echo ========================================
echo 统一推送模式完整测试
echo ========================================
echo.

echo 检查环境...
echo.

echo 1. 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请先安装Node.js
    pause
    exit /b 1
)
echo Node.js: 已安装

echo.
echo 2. 检查Agent可执行文件
if not exist "agent_full\build\Release\agent.exe" (
    echo 错误: 未找到Agent可执行文件
    echo 请先编译Agent: cd agent_full\build && cmake --build . --config Release
    pause
    exit /b 1
)
echo Agent: 已编译

echo.
echo 3. 检查插件文件
if not exist "agent_full\plugins\test_push_plugin.dll" (
    echo 错误: 未找到测试插件
    echo 请先编译插件并复制到plugins目录
    pause
    exit /b 1
)
echo 测试插件: 已就绪

echo.
echo 4. 检查配置文件
if not exist "agent_full\config.txt" (
    echo 错误: 未找到配置文件
    pause
    exit /b 1
)
echo 配置文件: 已就绪

echo.
echo ========================================
echo 开始测试
echo ========================================
echo.

echo 安装WebSocket依赖...
npm install ws >nul 2>&1
if %errorlevel% neq 0 (
    echo 警告: 无法安装ws模块，请手动安装: npm install ws
)

echo.
echo 启动测试服务器...
echo 服务器将在端口3000监听WebSocket连接
echo.

start "统一推送测试服务器" cmd /k "node test_server.js"

echo 等待服务器启动...
timeout /t 3 /nobreak >nul

echo.
echo 启动Agent (统一推送模式)...
echo Agent将连接到localhost:3000并开始推送数据
echo.
echo 观察要点:
echo - 服务器窗口将显示接收到的推送消息
echo - 包括心跳、系统信息和测试插件数据
echo - 每2秒会有测试数据推送
echo - 按Ctrl+C可停止Agent
echo.

cd agent_full
build\Release\agent.exe config.txt

echo.
echo 测试完成
pause
