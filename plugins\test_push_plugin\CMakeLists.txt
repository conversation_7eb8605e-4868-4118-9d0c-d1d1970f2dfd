cmake_minimum_required(VERSION 3.16)
project(TestPushPlugin VERSION 1.0.0 LANGUAGES C)

# Set C standard
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler options
if(MSVC)
    add_compile_options(/W3 /utf-8)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra)
endif()

# Source files
set(PLUGIN_SOURCES
    src/test_push_plugin.c
)

# Platform specific libraries
if(WIN32)
    set(PLATFORM_LIBS user32 kernel32)
else()
    set(PLATFORM_LIBS pthread)
endif()

# Create shared library
add_library(test_push_plugin SHARED ${PLUGIN_SOURCES})

# Link libraries
target_link_libraries(test_push_plugin ${PLATFORM_LIBS})

# Properties
set_target_properties(test_push_plugin PROPERTIES
    PREFIX ""
    OUTPUT_NAME "test_push_plugin"
    DEBUG_POSTFIX "_d"
)

# Windows specific settings
if(WIN32)
    set_target_properties(test_push_plugin PROPERTIES
        SUFFIX ".dll"
    )
else()
    set_target_properties(test_push_plugin PROPERTIES
        SUFFIX ".so"
    )
endif()

# Install
install(TARGETS test_push_plugin
    LIBRARY DESTINATION plugins
    RUNTIME DESTINATION plugins
)
