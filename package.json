{"name": "win", "version": "1.0.0", "description": "这是一个基于插件容器架构的分布式监控系统，支持远程DLL插件动态加载和执行。系统采用C语言开发轻量级Agent客户端，Node.js + TypeScript开发管理服务器，通过WebSocket实现双向实时通信，使用XOR加密确保数据传输安全。", "main": "test_server.js", "directories": {"doc": "docs"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"ws": "^8.18.3"}}