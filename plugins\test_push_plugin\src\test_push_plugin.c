#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <time.h>

#ifdef _WIN32
#include <windows.h>
#define SLEEP_MS(ms) Sleep(ms)
#else
#include <unistd.h>
#include <pthread.h>
#define SLEEP_MS(ms) usleep((ms) * 1000)
#endif

// 函数声明
__declspec(dllexport) int plugin_stop_push_mode(void);

// 插件状态
typedef struct {
    bool initialized;
    bool push_mode_enabled;
    volatile bool running;
    int push_interval;
    uint64_t message_count;
    char last_error[256];
    
    // 推送回调函数
    int (*push_callback)(const char* source_name, const char* data_type, const char* data, int priority);
    
    // 推送线程
#ifdef _WIN32
    HANDLE push_thread;
#else
    pthread_t push_thread;
#endif
} TestPluginState;

static TestPluginState g_plugin_state = {0};

// 获取当前时间戳（毫秒）
static uint64_t get_timestamp_ms(void)
{
#ifdef _WIN32
    return GetTickCount64();
#else
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (uint64_t)(tv.tv_sec * 1000 + tv.tv_usec / 1000);
#endif
}

// 生成测试数据
static char* generate_test_data(void)
{
    static int data_id = 0;
    data_id++;
    
    uint64_t timestamp = get_timestamp_ms();
    
    // 创建JSON格式的测试数据
    char* test_data = malloc(512);
    if (!test_data) {
        return NULL;
    }
    
    snprintf(test_data, 512,
        "{"
        "\"dataId\":%d,"
        "\"timestamp\":%llu,"
        "\"testValue\":%.2f,"
        "\"status\":\"%s\","
        "\"randomNumber\":%d,"
        "\"message\":\"Test push data from plugin\""
        "}",
        data_id,
        timestamp,
        (rand() % 10000) / 100.0,  // 随机测试值
        (data_id % 3 == 0) ? "warning" : "normal",  // 每3条消息一个警告
        rand() % 1000
    );
    
    return test_data;
}

// 推送线程函数
#ifdef _WIN32
DWORD WINAPI test_push_thread(LPVOID param)
#else
void* test_push_thread(void* param)
#endif
{
    TestPluginState* state = (TestPluginState*)param;
    
    printf("[TestPush] Push thread started with %d ms interval\n", state->push_interval);
    
    while (state->running && state->push_mode_enabled) {
        // 生成测试数据
        char* test_data = generate_test_data();
        if (test_data) {
            // 推送数据到Agent
            if (state->push_callback) {
                // 根据数据内容设置优先级
                int priority = (strstr(test_data, "\"status\":\"warning\"")) ? 8 : 5;
                int result = state->push_callback("test_push_plugin", "test_data", test_data, priority);
                
                if (result == 0) {
                    state->message_count++;
                    printf("[TestPush] Message %llu pushed successfully (priority=%d)\n", 
                           state->message_count, priority);
                } else {
                    printf("[TestPush] Failed to push message: %d\n", result);
                }
            }
            
            free(test_data);
        } else {
            printf("[TestPush] Failed to generate test data\n");
        }
        
        // 等待下一次推送
        SLEEP_MS(state->push_interval);
    }
    
    printf("[TestPush] Push thread ended. Total messages sent: %llu\n", state->message_count);
    
#ifdef _WIN32
    return 0;
#else
    return NULL;
#endif
}

// 插件初始化
__declspec(dllexport) int plugin_init(void* agent_context)
{
    printf("[TestPush] Initializing test push plugin...\n");
    
    memset(&g_plugin_state, 0, sizeof(g_plugin_state));
    g_plugin_state.push_interval = 2000; // 2秒间隔
    g_plugin_state.initialized = true;
    
    // 初始化随机数种子
    srand((unsigned int)time(NULL));
    
    printf("[TestPush] Test push plugin initialized\n");
    return 0;
}

// 插件清理
__declspec(dllexport) int plugin_cleanup(void)
{
    printf("[TestPush] Cleaning up test push plugin...\n");
    
    // 停止推送模式
    if (g_plugin_state.push_mode_enabled) {
        plugin_stop_push_mode();
    }
    
    g_plugin_state.initialized = false;
    
    printf("[TestPush] Test push plugin cleanup completed\n");
    return 0;
}

// 注册推送回调
__declspec(dllexport) int plugin_register_push_callback(int (*push_callback)(const char* source_name,
                                                                             const char* data_type,
                                                                             const char* data,
                                                                             int priority))
{
    if (!push_callback) {
        return -1;
    }
    
    g_plugin_state.push_callback = push_callback;
    printf("[TestPush] Push callback registered\n");
    return 0;
}

// 启动推送模式
__declspec(dllexport) int plugin_start_push_mode(void)
{
    if (!g_plugin_state.initialized) {
        return -1;
    }
    
    if (g_plugin_state.push_mode_enabled) {
        return 0; // 已经启动
    }
    
    g_plugin_state.push_mode_enabled = true;
    g_plugin_state.running = true;
    g_plugin_state.message_count = 0;
    
    // 启动推送线程
#ifdef _WIN32
    g_plugin_state.push_thread = CreateThread(NULL, 0, test_push_thread, &g_plugin_state, 0, NULL);
    if (g_plugin_state.push_thread == NULL) {
        g_plugin_state.push_mode_enabled = false;
        g_plugin_state.running = false;
        printf("[TestPush] Failed to create push thread\n");
        return -1;
    }
#else
    if (pthread_create(&g_plugin_state.push_thread, NULL, test_push_thread, &g_plugin_state) != 0) {
        g_plugin_state.push_mode_enabled = false;
        g_plugin_state.running = false;
        printf("[TestPush] Failed to create push thread\n");
        return -1;
    }
#endif
    
    printf("[TestPush] Push mode started with %d ms interval\n", g_plugin_state.push_interval);
    return 0;
}

// 停止推送模式
__declspec(dllexport) int plugin_stop_push_mode(void)
{
    if (!g_plugin_state.push_mode_enabled) {
        return 0;
    }
    
    g_plugin_state.running = false;
    g_plugin_state.push_mode_enabled = false;
    
    // 等待推送线程结束
#ifdef _WIN32
    if (g_plugin_state.push_thread != NULL) {
        WaitForSingleObject(g_plugin_state.push_thread, 5000);
        CloseHandle(g_plugin_state.push_thread);
        g_plugin_state.push_thread = NULL;
    }
#else
    if (g_plugin_state.push_thread != 0) {
        pthread_join(g_plugin_state.push_thread, NULL);
        g_plugin_state.push_thread = 0;
    }
#endif
    
    printf("[TestPush] Push mode stopped. Total messages sent: %llu\n", g_plugin_state.message_count);
    return 0;
}

// 配置推送参数
__declspec(dllexport) int plugin_configure_push(const char* config_json)
{
    if (!config_json) {
        return -1;
    }
    
    // 简单的配置解析
    if (strstr(config_json, "\"interval\":")) {
        const char* interval_str = strstr(config_json, "\"interval\":");
        if (interval_str) {
            int interval = atoi(interval_str + 11);
            if (interval > 0 && interval <= 60000) {
                g_plugin_state.push_interval = interval;
                printf("[TestPush] Push interval updated to %d ms\n", interval);
                return 0;
            }
        }
    }
    
    printf("[TestPush] Invalid configuration: %s\n", config_json);
    return -1;
}

// 获取插件信息
__declspec(dllexport) const char* plugin_get_plugin_info(void)
{
    static char info[512];
    snprintf(info, sizeof(info),
        "{"
        "\"name\":\"test_push_plugin\","
        "\"version\":\"1.0.0\","
        "\"description\":\"Simple test plugin for unified push mode validation\","
        "\"author\":\"Agent System\","
        "\"push_mode_supported\":true,"
        "\"initialized\":%s,"
        "\"push_enabled\":%s,"
        "\"messages_sent\":%llu,"
        "\"push_interval\":%d"
        "}",
        g_plugin_state.initialized ? "true" : "false",
        g_plugin_state.push_mode_enabled ? "true" : "false",
        g_plugin_state.message_count,
        g_plugin_state.push_interval);
    
    return info;
}
