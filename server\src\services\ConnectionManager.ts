import { WebSocket } from 'ws';
import { EventEmitter } from 'events';
import { AgentConnection, AgentStats, AgentStatus } from '../types/Agent';
import { Message, MessageType } from '../types/Message';
import { WebSocketConfigManager } from '@config/websocket';
import { SecureRandom, MessageIntegrity } from '@utils/crypto';
import logger from '@utils/logger';
import { TimeHelper, ValidationHelper } from '@utils/helpers';

export interface ConnectionEvents {
  'agent-connected': (agentId: string, connection: AgentConnection) => void;
  'agent-disconnected': (agentId: string, reason: string) => void;
  'agent-message': (agentId: string, message: Message) => void;
  'agent-error': (agentId: string, error: Error) => void;
  'heartbeat-timeout': (agentId: string) => void;
  'max-connections-reached': () => void;
}

export class ConnectionManager extends EventEmitter {
  private static instance: ConnectionManager;
  private connections: Map<string, AgentConnection> = new Map();
  private heartbeatTimers: Map<string, NodeJS.Timeout> = new Map();
  private config = WebSocketConfigManager.getInstance().getConfig();
  private stats: AgentStats = {
    totalConnections: 0,
    activeConnections: 0,
    averageResponseTime: 0,
    dataTransferred: 0,
    errorsCount: 0
  };
  
  private constructor() {
    super();
    this.setupCleanupInterval();
  }
  
  public static getInstance(): ConnectionManager {
    if (!ConnectionManager.instance) {
      ConnectionManager.instance = new ConnectionManager();
    }
    return ConnectionManager.instance;
  }
  
  /**
   * 添加新的Agent连接
   */
  public addConnection(socket: WebSocket, remoteAddress: string): string | null {
    // 检查连接数限制
    if (this.connections.size >= this.config.maxConnections) {
      logger.warn(`达到最大连接数限制: ${this.config.maxConnections}`);
      this.emit('max-connections-reached');
      return null;
    }
    
    const agentId = SecureRandom.generateUUID();
    const connection: AgentConnection = {
      id: agentId,
      socket,
      status: AgentStatus.CONNECTED,
      lastHeartbeat: new Date(),
      connectTime: new Date(),
      loadedPlugins: [],
      remoteAddress: ValidationHelper.sanitizeString(remoteAddress)
    };
    
    this.connections.set(agentId, connection);
    this.setupSocketEventHandlers(agentId, socket);
    this.startHeartbeatTimer(agentId);
    
    // 更新统计信息
    this.stats.totalConnections++;
    this.stats.activeConnections = this.connections.size;
    
    logger.logAgentConnection(agentId, 'connected', { remoteAddress });
    this.emit('agent-connected', agentId, connection);
    
    return agentId;
  }
  
  /**
   * 移除Agent连接
   */
  public removeConnection(agentId: string, reason: string = 'unknown'): boolean {
    const connection = this.connections.get(agentId);
    if (!connection) {
      return false;
    }
    
    // 清理心跳定时器
    this.clearHeartbeatTimer(agentId);
    
    // 关闭WebSocket连接
    if (connection.socket.readyState === WebSocket.OPEN) {
      connection.socket.close();
    }
    
    // 移除连接记录
    this.connections.delete(agentId);
    
    // 更新统计信息
    this.stats.activeConnections = this.connections.size;
    
    logger.logAgentConnection(agentId, 'disconnected', { reason });
    this.emit('agent-disconnected', agentId, reason);
    
    return true;
  }
  
  /**
   * 获取Agent连接信息
   */
  public getConnection(agentId: string): AgentConnection | null {
    return this.connections.get(agentId) || null;
  }
  
  /**
   * 获取所有活跃连接
   */
  public getAllConnections(): AgentConnection[] {
    return Array.from(this.connections.values());
  }
  
  /**
   * 获取健康的连接
   */
  public getHealthyConnections(): AgentConnection[] {
    return this.getAllConnections().filter(conn => 
      conn.status === AgentStatus.CONNECTED &&
      !TimeHelper.isExpired(conn.lastHeartbeat.getTime(), this.config.heartbeatTimeout)
    );
  }
  
  /**
   * 向指定Agent发送消息
   */
  public async sendToAgent(agentId: string, message: Message): Promise<boolean> {
    const connection = this.connections.get(agentId);
    if (!connection || connection.socket.readyState !== WebSocket.OPEN) {
      logger.warn(`尝试向不存在或已断开的Agent发送消息: ${agentId}`);
      return false;
    }
    
    try {
      const messageStr = JSON.stringify(message);
      const checksum = MessageIntegrity.calculateChecksum(messageStr);
      const messageWithChecksum = { ...message, checksum };
      
      connection.socket.send(JSON.stringify(messageWithChecksum));
      
      // 更新统计信息
      this.stats.dataTransferred += messageStr.length;
      
      logger.logWebSocketEvent('message-sent', agentId, { type: message.type });
      return true;
    } catch (error) {
      logger.logError(error as Error, `发送消息到Agent ${agentId}`);
      this.stats.errorsCount++;
      return false;
    }
  }
  
  /**
   * 广播消息到所有活跃Agent
   */
  public async broadcastMessage(message: Omit<Message, 'agentId'>): Promise<number> {
    const healthyConnections = this.getHealthyConnections();
    let successCount = 0;
    
    const promises = healthyConnections.map(async (connection) => {
      const agentMessage = { ...message, agentId: connection.id } as Message;
      const success = await this.sendToAgent(connection.id, agentMessage);
      if (success) successCount++;
    });
    
    await Promise.all(promises);
    
    logger.info(`广播消息完成: ${successCount}/${healthyConnections.length} 成功`);
    return successCount;
  }
  
  /**
   * 更新Agent系统信息
   */
  public updateAgentSystemInfo(agentId: string, systemInfo: any): boolean {
    const connection = this.connections.get(agentId);
    if (!connection) {
      return false;
    }
    
    connection.systemInfo = systemInfo;
    logger.logAgentConnection(agentId, 'system-info-updated', systemInfo);
    return true;
  }
  
  /**
   * 获取统计信息
   */
  public getStats(): AgentStats {
    // 计算平均响应时间（简化实现）
    const connections = this.getAllConnections();
    const totalResponseTime = connections.reduce((sum, conn) => {
      return sum + (Date.now() - conn.connectTime.getTime());
    }, 0);
    
    this.stats.averageResponseTime = connections.length > 0 
      ? totalResponseTime / connections.length 
      : 0;
    
    return { ...this.stats };
  }
  
  /**
   * 设置Socket事件处理器
   */
  private setupSocketEventHandlers(agentId: string, socket: WebSocket): void {
    socket.on('message', (data) => {
      this.handleAgentMessage(agentId, data);
    });
    
    socket.on('close', (code, reason) => {
      this.removeConnection(agentId, `WebSocket closed: ${code} ${reason}`);
    });
    
    socket.on('error', (error) => {
      logger.logError(error, `Agent ${agentId} WebSocket错误`);
      this.emit('agent-error', agentId, error);
      this.stats.errorsCount++;
    });
    
    socket.on('pong', () => {
      this.updateHeartbeat(agentId);
    });
  }
  
  /**
   * 处理Agent消息
   */
  private handleAgentMessage(agentId: string, data: any): void {
    try {
      const message = JSON.parse(data.toString()) as Message;
      
      // 验证消息格式
      if (!this.validateMessage(message)) {
        logger.warn(`收到无效消息格式，来自Agent: ${agentId}`);
        return;
      }
      
      // 更新心跳时间
      if (message.type === MessageType.HEARTBEAT) {
        this.updateHeartbeat(agentId);
      }

      // 处理统一推送消息
      if (message.type === MessageType.UNIFIED_PUSH) {
        this.handleUnifiedPushMessage(agentId, message as any);
      }

      // 更新统计信息
      this.stats.dataTransferred += data.length;

      logger.logWebSocketEvent('message-received', agentId, { type: message.type });
      this.emit('agent-message', agentId, message);
      
    } catch (error) {
      logger.logError(error as Error, `解析Agent消息失败: ${agentId}`);
      this.stats.errorsCount++;
    }
  }

  /**
   * 处理统一推送消息
   */
  private handleUnifiedPushMessage(agentId: string, message: any): void {
    logger.info(`收到统一推送消息 from ${agentId}:`);
    logger.info(`  - 来源类型: ${message.sourceType}`);
    logger.info(`  - 来源名称: ${message.sourceName}`);
    logger.info(`  - 数据类型: ${message.dataType}`);
    logger.info(`  - 优先级: ${message.priority}`);

    if (message.payload) {
      try {
        const payload = JSON.parse(message.payload);
        logger.info(`  - 数据内容: ${JSON.stringify(payload, null, 2)}`);
      } catch (e) {
        logger.info(`  - 原始数据: ${message.payload}`);
      }
    }

    // 发送确认响应
    const connection = this.connections.get(agentId);
    if (connection && connection.socket.readyState === 1) {
      const ackMessage = {
        type: 'ack',
        messageId: message.messageId || Date.now().toString(),
        status: 'received',
        timestamp: Date.now()
      };

      connection.socket.send(JSON.stringify(ackMessage));
    }
  }

  /**
   * 验证消息格式
   */
  private validateMessage(message: any): message is Message {
    // 对于统一推送消息，使用不同的验证规则
    if (message.type === MessageType.UNIFIED_PUSH) {
      return (
        message &&
        typeof message.type === 'string' &&
        typeof message.sourceType === 'string' &&
        typeof message.sourceName === 'string' &&
        typeof message.dataType === 'string' &&
        typeof message.priority === 'number'
      );
    }

    return (
      message &&
      typeof message.type === 'string' &&
      typeof message.timestamp === 'number' &&
      typeof message.agentId === 'string' &&
      typeof message.messageId === 'string' &&
      message.data !== undefined
    );
  }
  
  /**
   * 开始心跳计时器
   */
  private startHeartbeatTimer(agentId: string): void {
    const timer = setTimeout(() => {
      logger.warn(`Agent心跳超时: ${agentId}`);
      this.emit('heartbeat-timeout', agentId);
      this.removeConnection(agentId, 'heartbeat timeout');
    }, this.config.heartbeatTimeout);
    
    this.heartbeatTimers.set(agentId, timer);
  }
  
  /**
   * 清理心跳计时器
   */
  private clearHeartbeatTimer(agentId: string): void {
    const timer = this.heartbeatTimers.get(agentId);
    if (timer) {
      clearTimeout(timer);
      this.heartbeatTimers.delete(agentId);
    }
  }
  
  /**
   * 更新心跳时间
   */
  private updateHeartbeat(agentId: string): void {
    const connection = this.connections.get(agentId);
    if (connection) {
      connection.lastHeartbeat = new Date();
      connection.status = AgentStatus.CONNECTED;
      
      // 重置心跳计时器
      this.clearHeartbeatTimer(agentId);
      this.startHeartbeatTimer(agentId);
      
      logger.debug(`Agent心跳更新: ${agentId}`);
    }
  }
  
  /**
   * 设置定期清理无效连接
   */
  private setupCleanupInterval(): void {
    setInterval(() => {
      const expiredConnections: string[] = [];
      
      this.connections.forEach((connection, agentId) => {
        if (TimeHelper.isExpired(connection.lastHeartbeat.getTime(), this.config.heartbeatTimeout)) {
          expiredConnections.push(agentId);
        }
      });
      
      expiredConnections.forEach(agentId => {
        this.removeConnection(agentId, 'cleanup - heartbeat expired');
      });
      
      if (expiredConnections.length > 0) {
        logger.info(`定期清理过期连接: ${expiredConnections.length}个`);
      }
      
    }, this.config.heartbeatInterval);
  }
  
  /**
   * 发送ping到所有连接
   */
  public pingAllConnections(): void {
    this.connections.forEach((connection, agentId) => {
      if (connection.socket.readyState === WebSocket.OPEN) {
        connection.socket.ping();
        logger.debug(`发送ping到Agent: ${agentId}`);
      }
    });
  }
  
  /**
   * 关闭所有连接
   */
  public closeAllConnections(reason: string = 'server shutdown'): void {
    const agentIds = Array.from(this.connections.keys());
    agentIds.forEach(agentId => {
      this.removeConnection(agentId, reason);
    });
    
    logger.info(`关闭所有连接: ${agentIds.length}个，原因: ${reason}`);
  }
  
  // 类型安全的事件监听器方法
  public override on<K extends keyof ConnectionEvents>(
    event: K, 
    listener: ConnectionEvents[K]
  ): this {
    return super.on(event, listener);
  }
  
  public override emit<K extends keyof ConnectionEvents>(
    event: K, 
    ...args: Parameters<ConnectionEvents[K]>
  ): boolean {
    return super.emit(event, ...args);
  }
}