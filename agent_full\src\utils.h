#ifndef UTILS_H
#define UTILS_H

#include "agent.h"

// Utility functions
uint64_t get_timestamp_ms(void);
void format_timestamp(char* buffer, size_t size);
char* create_json_message(const char* type, const char* agent_id, const char* data);
void free_json_message(char* message);
void get_system_info(char* buffer, size_t size);

// 统一推送相关函数
AgentResult send_push_message_to_server(AgentContext* ctx, const UnifiedPushData* push_data);

#endif // UTILS_H