cmake_minimum_required(VERSION 3.16)
project(AgentFull VERSION 1.0.0 LANGUAGES C)

# Set C standard
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler options
if(MSVC)
    add_compile_options(/W3 /utf-8)
    add_compile_definitions(_CRT_SECURE_NO_WARNINGS)
    add_compile_definitions(_WINSOCK_DEPRECATED_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra)
endif()

# Source files
set(AGENT_SOURCES
    src/main.c
    src/websocket_client.c
    src/logger.c
    src/config.c
    src/utils.c
    src/plugin_manager.c
    src/message_handler.c
    src/unified_push_manager.c
    src/message_queue.c
    src/system_plugins.c
)

# Header files
set(AGENT_HEADERS
    src/agent.h
    src/websocket_client.h
    src/logger.h
    src/config.h
    src/utils.h
    src/plugin_manager.h
    src/message_handler.h
)

# Platform specific libraries
if(WIN32)
    set(PLATFORM_LIBS ws2_32 wininet user32 kernel32)
else()
    set(PLATFORM_LIBS pthread dl)
endif()

# Create executable
add_executable(agent_full ${AGENT_SOURCES} ${AGENT_HEADERS})

# Link libraries
target_link_libraries(agent_full ${PLATFORM_LIBS})

# Include directories
target_include_directories(agent_full PRIVATE src)

# Properties
set_target_properties(agent_full PROPERTIES
    OUTPUT_NAME "agent"
    DEBUG_POSTFIX "_d"
)

# Install
install(TARGETS agent_full
    RUNTIME DESTINATION bin
)