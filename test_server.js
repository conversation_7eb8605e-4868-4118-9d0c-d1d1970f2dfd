const WebSocket = require('ws');
const http = require('http');

// 创建WebSocket服务器
const wss = new WebSocket.Server({
    port: 3000
});

console.log('='.repeat(60));
console.log('统一推送模式测试服务器');
console.log('='.repeat(60));
console.log('监听端口: 3000');
console.log('等待Agent连接...');
console.log('='.repeat(60));

// 存储连接的客户端
const clients = new Map();
let messageCount = 0;

wss.on('connection', function connection(ws, req) {
    const clientId = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    clients.set(clientId, {
        ws: ws,
        connectTime: new Date(),
        messageCount: 0,
        lastMessage: null
    });
    
    console.log(`\n[${new Date().toISOString()}] 新客户端连接: ${clientId}`);
    console.log(`当前连接数: ${clients.size}`);
    
    // 发送欢迎消息
    ws.send(JSON.stringify({
        type: 'welcome',
        message: 'Connected to unified push test server',
        clientId: clientId,
        timestamp: Date.now()
    }));
    
    ws.on('message', function incoming(data) {
        try {
            const message = JSON.parse(data.toString());
            messageCount++;
            
            const client = clients.get(clientId);
            if (client) {
                client.messageCount++;
                client.lastMessage = new Date();
            }
            
            console.log(`\n[${new Date().toISOString()}] 收到消息 #${messageCount}:`);
            console.log(`客户端: ${clientId}`);
            console.log(`消息类型: ${message.type || 'unknown'}`);
            
            // 处理不同类型的消息
            switch (message.type) {
                case 'heartbeat':
                    console.log(`心跳消息 - Agent ID: ${message.agent_id}`);
                    if (message.data) {
                        const heartbeatData = JSON.parse(message.data);
                        console.log(`  - 状态: ${heartbeatData.status}`);
                        console.log(`  - 运行时间: ${heartbeatData.uptime}ms`);
                        console.log(`  - 插件数量: ${heartbeatData.plugin_count}`);
                    }
                    break;
                    
                case 'system_info':
                    console.log(`系统信息 - Agent ID: ${message.agent_id}`);
                    if (message.data) {
                        const sysData = JSON.parse(message.data);
                        console.log(`  - CPU使用率: ${sysData.cpu_usage}%`);
                        console.log(`  - 内存使用率: ${sysData.memory_usage}%`);
                        console.log(`  - 磁盘使用率: ${sysData.disk_usage}%`);
                    }
                    break;
                    
                case 'unified_push':
                    console.log(`统一推送数据:`);
                    console.log(`  - 来源类型: ${message.sourceType}`);
                    console.log(`  - 来源名称: ${message.sourceName}`);
                    console.log(`  - 数据类型: ${message.dataType}`);
                    console.log(`  - 优先级: ${message.priority}`);
                    
                    if (message.payload) {
                        try {
                            const payload = JSON.parse(message.payload);
                            console.log(`  - 数据内容:`);
                            if (payload.dataId) console.log(`    * 数据ID: ${payload.dataId}`);
                            if (payload.testValue) console.log(`    * 测试值: ${payload.testValue}`);
                            if (payload.status) console.log(`    * 状态: ${payload.status}`);
                            if (payload.randomNumber) console.log(`    * 随机数: ${payload.randomNumber}`);
                            if (payload.message) console.log(`    * 消息: ${payload.message}`);
                        } catch (e) {
                            console.log(`    * 原始数据: ${message.payload}`);
                        }
                    }
                    break;
                    
                default:
                    console.log(`未知消息类型: ${JSON.stringify(message, null, 2)}`);
                    break;
            }
            
            // 发送确认响应
            ws.send(JSON.stringify({
                type: 'ack',
                messageId: message.messageId || messageCount,
                status: 'received',
                timestamp: Date.now()
            }));
            
        } catch (error) {
            console.error(`解析消息失败: ${error.message}`);
            console.error(`原始数据: ${data.toString()}`);
        }
    });
    
    ws.on('close', function close() {
        clients.delete(clientId);
        console.log(`\n[${new Date().toISOString()}] 客户端断开连接: ${clientId}`);
        console.log(`当前连接数: ${clients.size}`);
    });
    
    ws.on('error', function error(err) {
        console.error(`\n[${new Date().toISOString()}] WebSocket错误 (${clientId}): ${err.message}`);
    });
});

// 定期显示统计信息
setInterval(() => {
    if (clients.size > 0) {
        console.log(`\n[统计] 当前时间: ${new Date().toISOString()}`);
        console.log(`[统计] 连接数: ${clients.size}, 总消息数: ${messageCount}`);
        
        clients.forEach((client, clientId) => {
            const uptime = Math.floor((Date.now() - client.connectTime.getTime()) / 1000);
            const lastMsg = client.lastMessage ? 
                Math.floor((Date.now() - client.lastMessage.getTime()) / 1000) : 'N/A';
            console.log(`[统计] ${clientId}: 消息${client.messageCount}条, 在线${uptime}秒, 最后消息${lastMsg}秒前`);
        });
    }
}, 30000); // 每30秒显示一次统计

console.log('测试服务器已启动，等待Agent连接...');

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n正在关闭服务器...');
    wss.close(() => {
        console.log('服务器已关闭');
        process.exit(0);
    });
});
