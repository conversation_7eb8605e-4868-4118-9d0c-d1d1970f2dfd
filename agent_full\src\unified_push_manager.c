#include "agent.h"
#include "logger.h"
#include "websocket_client.h"
#include "utils.h"

// 全局Agent上下文指针（供插件调用）
static AgentContext* g_agent_context = NULL;

// 推送线程函数声明
#ifdef _WIN32
DWORD WINAPI push_thread_proc(LPVOID param);
#else
void* push_thread_proc(void* param);
#endif

// 初始化统一推送管理器
AgentResult unified_push_manager_init(UnifiedPushManager* manager, int queue_size, int thread_count)
{
    if (!manager) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    memset(manager, 0, sizeof(UnifiedPushManager));
    
    // 初始化消息队列
    AgentResult result = message_queue_init(&manager->push_queue, queue_size);
    if (result != AGENT_SUCCESS) {
        return result;
    }
    
    manager->thread_count = (thread_count > MAX_PUSH_THREADS) ? MAX_PUSH_THREADS : thread_count;
    manager->enabled = false;
    manager->running = false;
    
    LOG_INFO_MSG("Unified push manager initialized with queue_size=%d, thread_count=%d", 
                 queue_size, manager->thread_count);
    
    return AGENT_SUCCESS;
}

// 清理统一推送管理器
void unified_push_manager_cleanup(UnifiedPushManager* manager)
{
    if (!manager) {
        return;
    }
    
    // 停止推送线程
    unified_push_manager_stop(manager);
    
    // 清理消息队列
    message_queue_cleanup(&manager->push_queue);
    
    LOG_INFO_MSG("Unified push manager cleanup completed");
}

// 启动推送管理器
AgentResult unified_push_manager_start(UnifiedPushManager* manager, AgentContext* ctx)
{
    if (!manager || !ctx) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    if (manager->running) {
        return AGENT_SUCCESS; // 已经在运行
    }
    
    manager->enabled = true;
    manager->running = true;
    
    // 启动推送线程
    for (int i = 0; i < manager->thread_count; i++) {
#ifdef _WIN32
        manager->push_threads[i] = CreateThread(NULL, 0, push_thread_proc, ctx, 0, NULL);
        if (manager->push_threads[i] == NULL) {
            LOG_ERROR_MSG("Failed to create push thread %d", i);
            unified_push_manager_stop(manager);
            return AGENT_ERROR_SYSTEM;
        }
#else
        if (pthread_create(&manager->push_threads[i], NULL, push_thread_proc, ctx) != 0) {
            LOG_ERROR_MSG("Failed to create push thread %d", i);
            unified_push_manager_stop(manager);
            return AGENT_ERROR_SYSTEM;
        }
#endif
    }
    
    LOG_INFO_MSG("Unified push manager started with %d threads", manager->thread_count);
    return AGENT_SUCCESS;
}

// 停止推送管理器
void unified_push_manager_stop(UnifiedPushManager* manager)
{
    if (!manager || !manager->running) {
        return;
    }
    
    manager->running = false;
    manager->enabled = false;
    
    // 等待所有推送线程结束
    for (int i = 0; i < manager->thread_count; i++) {
#ifdef _WIN32
        if (manager->push_threads[i] != NULL) {
            WaitForSingleObject(manager->push_threads[i], 5000);
            CloseHandle(manager->push_threads[i]);
            manager->push_threads[i] = NULL;
        }
#else
        if (manager->push_threads[i] != 0) {
            pthread_join(manager->push_threads[i], NULL);
            manager->push_threads[i] = 0;
        }
#endif
    }
    
    LOG_INFO_MSG("Unified push manager stopped");
}

// 暂停推送管理器
AgentResult unified_push_manager_pause(UnifiedPushManager* manager)
{
    if (!manager) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    manager->enabled = false;
    LOG_INFO_MSG("Unified push manager paused");
    return AGENT_SUCCESS;
}

// 恢复推送管理器
AgentResult unified_push_manager_resume(UnifiedPushManager* manager)
{
    if (!manager) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    manager->enabled = true;
    LOG_INFO_MSG("Unified push manager resumed");
    return AGENT_SUCCESS;
}

// 推送线程主函数
#ifdef _WIN32
DWORD WINAPI push_thread_proc(LPVOID param)
#else
void* push_thread_proc(void* param)
#endif
{
    AgentContext* ctx = (AgentContext*)param;
    UnifiedPushManager* manager = &ctx->push_manager;
    
    LOG_INFO_MSG("Push thread started");
    
    while (manager->running) {
        if (!manager->enabled) {
            SLEEP_MS(100);
            continue;
        }
        
        UnifiedPushData push_data;
        if (message_queue_pop(&manager->push_queue, &push_data, 1000)) {
            // 发送推送消息到服务器
            AgentResult result = send_push_message_to_server(ctx, &push_data);
            
            if (result == AGENT_SUCCESS) {
                manager->total_pushed++;
                LOG_DEBUG_MSG("Push message sent: %s:%s", push_data.source_name, push_data.data_type);
            } else {
                manager->failed_pushes++;
                
                // 重试逻辑
                if (push_data.retry_count < ctx->config.max_retry_count) {
                    push_data.retry_count++;
                    manager->retry_count++;
                    
                    // 重新加入队列
                    if (message_queue_push(&manager->push_queue, &push_data) != AGENT_SUCCESS) {
                        LOG_WARN_MSG("Failed to requeue message for retry");
                        if (push_data.data) {
                            free(push_data.data);
                        }
                    }
                } else {
                    LOG_ERROR_MSG("Max retry count reached for message: %s", push_data.message_id);
                    if (push_data.data) {
                        free(push_data.data);
                    }
                }
            }
        }
    }
    
    LOG_INFO_MSG("Push thread ended");
    
#ifdef _WIN32
    return 0;
#else
    return NULL;
#endif
}

// 注册全局Agent上下文
AgentResult agent_register_global_context(AgentContext* ctx)
{
    if (!ctx) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    g_agent_context = ctx;
    LOG_INFO_MSG("Global agent context registered");
    return AGENT_SUCCESS;
}

// Agent推送API（供插件调用）
int agent_push_data(const char* source_name, const char* data_type, const char* data, int priority)
{
    if (!g_agent_context || !source_name || !data_type || !data) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    if (!g_agent_context->push_manager.enabled) {
        return AGENT_ERROR_INVALID_STATE;
    }
    
    // 创建推送消息
    UnifiedPushData push_data = {0};
    strncpy(push_data.source_type, "plugin", sizeof(push_data.source_type) - 1);
    strncpy(push_data.source_name, source_name, sizeof(push_data.source_name) - 1);
    strncpy(push_data.data_type, data_type, sizeof(push_data.data_type) - 1);
    push_data.data = _strdup(data);
    push_data.priority = (priority < 1) ? 1 : ((priority > 10) ? 10 : priority);
    push_data.timestamp = get_timestamp_ms();
    push_data.retry_count = 0;
    
    // 生成消息ID
    snprintf(push_data.message_id, sizeof(push_data.message_id), 
             "%s_%llu_%d", source_name, push_data.timestamp, rand() % 1000);
    
    // 加入推送队列
    AgentResult result = message_queue_push(&g_agent_context->push_manager.push_queue, &push_data);
    if (result != AGENT_SUCCESS) {
        if (push_data.data) {
            free(push_data.data);
        }
        
        if (result == AGENT_ERROR_QUEUE_FULL) {
            g_agent_context->push_manager.queue_overflows++;
        }
        
        return result;
    }
    
    return AGENT_SUCCESS;
}
