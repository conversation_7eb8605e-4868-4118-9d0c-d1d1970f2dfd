#include "agent.h"
#include "logger.h"

// 初始化消息队列
AgentResult message_queue_init(MessageQueue* queue, int capacity)
{
    if (!queue || capacity <= 0) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
    memset(queue, 0, sizeof(MessageQueue));
    
    // 分配消息缓冲区
    queue->messages = (UnifiedPushData*)malloc(capacity * sizeof(UnifiedPushData));
    if (!queue->messages) {
        return AGENT_ERROR_MEMORY;
    }
    
    memset(queue->messages, 0, capacity * sizeof(UnifiedPushData));
    
    queue->capacity = capacity;
    queue->count = 0;
    queue->head = 0;
    queue->tail = 0;
    
    // 初始化同步对象
#ifdef _WIN32
    InitializeCriticalSection(&queue->mutex);
    queue->semaphore = CreateSemaphore(NULL, 0, capacity, NULL);
    if (queue->semaphore == NULL) {
        DeleteCriticalSection(&queue->mutex);
        free(queue->messages);
        return AGENT_ERROR_SYSTEM;
    }
#else
    if (pthread_mutex_init(&queue->mutex, NULL) != 0) {
        free(queue->messages);
        return AGENT_ERROR_SYSTEM;
    }
    
    if (pthread_cond_init(&queue->condition, NULL) != 0) {
        pthread_mutex_destroy(&queue->mutex);
        free(queue->messages);
        return AGENT_ERROR_SYSTEM;
    }
#endif
    
    LOG_DEBUG_MSG("Message queue initialized with capacity %d", capacity);
    return AGENT_SUCCESS;
}

// 清理消息队列
void message_queue_cleanup(MessageQueue* queue)
{
    if (!queue) {
        return;
    }
    
    // 清理队列中剩余的消息
    if (queue->messages) {
        for (int i = 0; i < queue->capacity; i++) {
            if (queue->messages[i].data) {
                free(queue->messages[i].data);
                queue->messages[i].data = NULL;
            }
        }
        free(queue->messages);
        queue->messages = NULL;
    }
    
    // 清理同步对象
#ifdef _WIN32
    if (queue->semaphore) {
        CloseHandle(queue->semaphore);
        queue->semaphore = NULL;
    }
    DeleteCriticalSection(&queue->mutex);
#else
    pthread_cond_destroy(&queue->condition);
    pthread_mutex_destroy(&queue->mutex);
#endif
    
    LOG_DEBUG_MSG("Message queue cleanup completed");
}

// 向队列添加消息
AgentResult message_queue_push(MessageQueue* queue, const UnifiedPushData* data)
{
    if (!queue || !data) {
        return AGENT_ERROR_INVALID_PARAM;
    }
    
#ifdef _WIN32
    EnterCriticalSection(&queue->mutex);
#else
    pthread_mutex_lock(&queue->mutex);
#endif
    
    // 检查队列是否已满
    if (queue->count >= queue->capacity) {
#ifdef _WIN32
        LeaveCriticalSection(&queue->mutex);
#else
        pthread_mutex_unlock(&queue->mutex);
#endif
        return AGENT_ERROR_QUEUE_FULL;
    }
    
    // 复制消息数据
    queue->messages[queue->tail] = *data;
    
    // 深拷贝数据字符串
    if (data->data) {
        queue->messages[queue->tail].data = _strdup(data->data);
        if (!queue->messages[queue->tail].data) {
#ifdef _WIN32
            LeaveCriticalSection(&queue->mutex);
#else
            pthread_mutex_unlock(&queue->mutex);
#endif
            return AGENT_ERROR_MEMORY;
        }
    }
    
    // 更新队列指针
    queue->tail = (queue->tail + 1) % queue->capacity;
    queue->count++;
    
#ifdef _WIN32
    LeaveCriticalSection(&queue->mutex);
    
    // 通知等待的线程
    ReleaseSemaphore(queue->semaphore, 1, NULL);
#else
    pthread_cond_signal(&queue->condition);
    pthread_mutex_unlock(&queue->mutex);
#endif
    
    return AGENT_SUCCESS;
}

// 从队列获取消息
bool message_queue_pop(MessageQueue* queue, UnifiedPushData* data, int timeout_ms)
{
    if (!queue || !data) {
        return false;
    }
    
#ifdef _WIN32
    // 等待信号量
    DWORD wait_result = WaitForSingleObject(queue->semaphore, timeout_ms);
    if (wait_result != WAIT_OBJECT_0) {
        return false; // 超时或错误
    }
    
    EnterCriticalSection(&queue->mutex);
    
    if (queue->count == 0) {
        LeaveCriticalSection(&queue->mutex);
        return false;
    }
    
    // 复制消息数据
    *data = queue->messages[queue->head];
    
    // 清空原位置（避免重复释放）
    memset(&queue->messages[queue->head], 0, sizeof(UnifiedPushData));
    
    // 更新队列指针
    queue->head = (queue->head + 1) % queue->capacity;
    queue->count--;
    
    LeaveCriticalSection(&queue->mutex);
    
#else
    pthread_mutex_lock(&queue->mutex);
    
    // 等待条件变量
    struct timespec ts;
    clock_gettime(CLOCK_REALTIME, &ts);
    ts.tv_sec += timeout_ms / 1000;
    ts.tv_nsec += (timeout_ms % 1000) * 1000000;
    if (ts.tv_nsec >= 1000000000) {
        ts.tv_sec++;
        ts.tv_nsec -= 1000000000;
    }
    
    while (queue->count == 0) {
        int result = pthread_cond_timedwait(&queue->condition, &queue->mutex, &ts);
        if (result == ETIMEDOUT) {
            pthread_mutex_unlock(&queue->mutex);
            return false;
        }
    }
    
    // 复制消息数据
    *data = queue->messages[queue->head];
    
    // 清空原位置
    memset(&queue->messages[queue->head], 0, sizeof(UnifiedPushData));
    
    // 更新队列指针
    queue->head = (queue->head + 1) % queue->capacity;
    queue->count--;
    
    pthread_mutex_unlock(&queue->mutex);
#endif
    
    return true;
}

// 获取队列大小
int message_queue_size(const MessageQueue* queue)
{
    if (!queue) {
        return -1;
    }
    
#ifdef _WIN32
    EnterCriticalSection((CRITICAL_SECTION*)&queue->mutex);
    int size = queue->count;
    LeaveCriticalSection((CRITICAL_SECTION*)&queue->mutex);
#else
    pthread_mutex_lock((pthread_mutex_t*)&queue->mutex);
    int size = queue->count;
    pthread_mutex_unlock((pthread_mutex_t*)&queue->mutex);
#endif
    
    return size;
}

// 检查队列是否已满
bool message_queue_is_full(const MessageQueue* queue)
{
    if (!queue) {
        return true;
    }
    
#ifdef _WIN32
    EnterCriticalSection((CRITICAL_SECTION*)&queue->mutex);
    bool is_full = (queue->count >= queue->capacity);
    LeaveCriticalSection((CRITICAL_SECTION*)&queue->mutex);
#else
    pthread_mutex_lock((pthread_mutex_t*)&queue->mutex);
    bool is_full = (queue->count >= queue->capacity);
    pthread_mutex_unlock((pthread_mutex_t*)&queue->mutex);
#endif
    
    return is_full;
}
